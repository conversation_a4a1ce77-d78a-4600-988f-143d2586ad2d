import { SectionSearchForm, Layout, PaginationParams, Section, SectionLayoutFile } from '@/types';
import { RestPageResultResponse, RestResultResponse } from '@chances/portal_common_core';
import { SectionApi } from '@/api/section-api.ts';
import sectionEntityJson from './json/section/section-entity.json';


export class MockSectionApi implements SectionApi {

    /**
     * 根据编码获取楼层定义
     *
     * @param code 楼层定义编码
     * */
    getSectionByCode(code: string): Promise<RestResultResponse<Section>> {
        return Promise.resolve({ code: 200, msg: 'ok', result: sectionEntityJson as unknown as Section });
    }

    /**
     * 分页获取楼层定义列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getSections(params: Partial<SectionSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Section>> {
        return Promise.resolve({ code: 200, msg: 'ok', page: {} as any, result: [] as Section[] });
    }

    /**
     * 修改楼层定义信息
     *
     * @param code 楼层定义 code
     * @param section 楼层定义
     * */
    updateSection(code: string, section: Section): Promise<RestResultResponse<Section>> {
        return Promise.resolve({ code: 200, msg: 'ok', result: {} as Section });
    }

    /**
     * 新增、修改楼层定义布局
     *
     * @param sectionCode 楼层定义编码
     * @param layout 楼层定义布局
     * */
    updateSectionLayout(sectionCode: string, layout: SectionLayoutFile): Promise<RestResultResponse<Section>> {
        return Promise.resolve({ code: 200, msg: 'ok', result: {} as Section });
    }

    /**
     * 楼层定义另存为布局
     *
     * @param sectionCode 楼层定义编码
     * @param sectionLayoutFile 楼层定义布局文件
     * @param layoutForm 布局表单
     * */
    saveSectionAsLayout(sectionCode: string, sectionLayoutFile: SectionLayoutFile, layoutForm: Partial<Layout>): Promise<RestResultResponse<Layout>> {
        return Promise.resolve({ code: 200, msg: 'ok', result: {} as Layout });
    }
}
