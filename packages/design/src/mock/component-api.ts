import { RestPageResultResponse } from '@chances/portal_common_core';
import { Component, ComponentStyle, ComponentSearchForm, ComponentStyleSearchForm, PaginationParams } from '@/types';
import { ComponentApi } from '@/api';
import componentStyleJson from './json/component-style/component-style.json';

export class MockComponentApi implements ComponentApi {

    /**
     * 查询组件列表
     *
     * @param prams 查询表单
     * @param pageInfo 分页参数
     */
    getComponents(prams: Partial<ComponentSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Component>> {
        return Promise.resolve({ code: 200, msg: 'ok', page: {} as any, result: [] as Component[] });
    }

    /**
     * 查询组件样式列表
     *
     * @param params 查询组件样式的参数
     * @param pageInfo 分页参数
     * */
    getComponentStyles(params: Partial<ComponentStyleSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<ComponentStyle>> {
        return Promise.resolve({
            code: 200,
            msg: 'ok',
            page: {} as any,
            result: componentStyleJson as unknown as ComponentStyle[],
        });
    }
}
