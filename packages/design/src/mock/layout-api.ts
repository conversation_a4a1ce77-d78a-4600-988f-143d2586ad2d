import { LayoutApi } from '@/api';
import { RestPageResultResponse, RestResultResponse } from '@chances/portal_common_core';
import layoutEntity from './json/layout/layout-entity.json';
import { LayoutSearchForm, Layout, PaginationParams, SectionLayoutLayoutFile } from '@/types';

export class MockLayoutApi implements LayoutApi {

    /**
     * 根据编码获取布局
     * */
    getLayoutByCode(code: string): Promise<RestResultResponse<Layout>> {
        return Promise.resolve({
            code: 200, msg: 'ok', result: {
                code: 'section_layout_001',
                layoutVersion: 1,
            } as unknown as Layout,
        });
    }

    /**
     * 更新布局
     * */
    updateLayout(code: string, data: Layout): Promise<RestResultResponse<Layout>> {
        return Promise.resolve({
            code: 200, msg: 'ok', result: {} as unknown as Layout,
        });
    }

    /**
     * 更新布局文件
     * */
    updateLayoutFile(code: string, data: SectionLayoutLayoutFile): Promise<RestResultResponse<Layout>> {
        return Promise.resolve({
            code: 200, msg: 'ok', result: {} as unknown as Layout,
        });
    }

    /**
     * 分页获取布局列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLayouts(params: Partial<LayoutSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Layout>> {
        return Promise.resolve({
            code: 200,
            msg: 'ok',
            page: {} as any,
            result: layoutEntity as unknown as Layout[],
        });
    }
}
