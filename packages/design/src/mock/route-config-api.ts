import { RestPageResultResponse } from '@chances/portal_common_core';
import { LinkTypeSearchForm, PaginationParams, LinkType } from '@/types';
import { LinkTypeApi } from '@/api';

export class MockLinkTypeApi implements LinkTypeApi {
    /**
     * 分页获取路由配置列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLinkTypes(params: Partial<LinkTypeSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<LinkType>> {
        return Promise.resolve({
            code: 200,
            msg: 'ok',
            page: {} as any,
            result: [] as unknown as LinkType[],
        });
    }
}
