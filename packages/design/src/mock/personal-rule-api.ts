import { RestPageResultResponse } from '@chances/portal_common_core';
import { PersonalRuleSearchForm, PaginationParams, PersonalRule } from '@/types';
import { PersonalRuleApi } from '@/api/personal-rule-api.ts';

export class MockPersonalRuleApi implements PersonalRuleApi {
    /**
     * 分页获取推荐策略
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getPersonalRules(params: Partial<PersonalRuleSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<PersonalRule>> {
        return Promise.resolve({
            code: 200,
            msg: 'ok',
            page: {} as any,
            result: [] as unknown as PersonalRule[],
        });
    }
}
