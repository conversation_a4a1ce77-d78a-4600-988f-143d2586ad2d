<template>
    <div class="relative w-full" :style="{height: canvasHeight}">
        <div ref="horizontalRef" class="horizontal absolute w-full h-[30px]" />
        <div ref="verticalRef" class="vertical absolute w-[30px] h-full" />

        <div class="absolute bottom-5 right-5 flex items-center gap-2 bg-white p-1.5 rounded shadow-md z-[22]">
            <el-button @click="resetView" icon="Refresh"
                       size="small" title="重置视图"
                       class="flex items-center justify-center px-2 py-1 text-xs border border-gray-200 bg-white rounded hover:bg-gray-50"
            />
            <div class="h-4 w-[1px] bg-gray-200"></div>
            <el-button
                @mousedown="(e) => { if (!e.currentTarget.disabled) zoomLongPress('out') }"
                @mouseup="clearZoomLongPress"
                @mouseleave="clearZoomLongPress"
                icon="Minus"
                size="small"
                :disabled="scale <= MIN_SCALE"
                class="w-6 h-6 border border-gray-200 bg-white rounded flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            />
            <span class="min-w-[48px] text-center text-sm">{{ Math.round(scale * 100) }}%</span>
            <el-button
                @mousedown="(e) => { if (!e.currentTarget.disabled) zoomLongPress('in') }"
                @mouseup="clearZoomLongPress"
                @mouseleave="clearZoomLongPress"
                icon="Plus"
                size="small"
                :disabled="scale >= MAX_SCALE"
                class="w-6 h-6 border border-gray-200 bg-white rounded flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            />
        </div>

        <slot name="navigation" />

        <div id="base-canvas-container"
             ref="containerRef"
             class="container absolute top-[30px] left-[30px] right-0 bottom-0 overflow-auto bg-white box-border z-10"
             :style="containerStyle">
            <div ref="contentRef" class="origin-top-left min-w-full min-h-full transition-transform duration-150 ease-out"
                 :style="contentStyle">
                <slot name="overlay" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, inject, nextTick, onBeforeUnmount, onMounted, provide, ref, watch } from 'vue';
    import Guides from '@scena/guides';

    // 基础画布
    defineOptions({
        name: 'BaseCanvas',
    });

    // props 默认值
    const props = withDefaults(defineProps<{
        // 标尺最大尺寸
        maxRulerSize?: number;
        // 最小缩放比例
        minScale?: number;
        // 最大缩放比例
        maxScale?: number;
        // 缩放步进
        zoomStep?: number;
        // 内容宽度
        contentWidth?: number;
        // 内容高度
        contentHeight?: number;
        // 画布高度
        canvasHeight?: string;
    }>(), {
        maxRulerSize: 999999,
        minScale: 0.5,
        maxScale: 2.4,
        zoomStep: 0.01,
        contentWidth: undefined,
        contentHeight: undefined,
        canvasHeight: '100%',
    });

    // 模板引用
    const containerRef = ref<HTMLElement>();
    const horizontalRef = ref<HTMLElement>();
    const verticalRef = ref<HTMLElement>();

    // 内容区域引用
    const contentRef = ref<HTMLElement>();

    // 当前缩放比例
    const scale = ref<number>(1);

    // 标尺就绪状态
    const rulersReady = ref(false);

    // 提供缩放给子组件
    provide('epg_scale', scale);

    // 中心面板宽度
    const centerWidth = inject('centerWidth', 1200);

    // centerWidth 响应式取值
    const getCenterWidth = computed(() => (centerWidth as any).value ?? centerWidth);
    const getContentWidth = computed(() => props.contentWidth);
    const getContentHeight = computed(() => props.contentHeight);

    // 缩放方向类型
    type ZoomDirection = 'in' | 'out';

    // 最小缩放
    const MIN_SCALE = computed(() => props.minScale);
    // 最大缩放
    const MAX_SCALE = computed(() => props.maxScale);
    // 缩放步进
    const ZOOM_STEP = computed(() => props.zoomStep);

    // 内容区域偏移
    const contentPosition = ref<{
        x: number;
        y: number;
    }>({ x: 0, y: 0 });

    // 标尺实例
    const rulers = {
        // 横向标尺
        horizontal: ref<Guides | null>(null),
        // 纵向标尺
        vertical: ref<Guides | null>(null),
    };

    // 标尺配置
    const rulerConfig = {
        // 显示拖拽位置
        displayDragPos: true,
        // 用 ResizeObserver 监听
        useResizeObserver: true,
        backgroundColor: '#f5f5f5',
        lineColor: '#ccc',
        textColor: '#666',
        range: [0, props.maxRulerSize] as [number, number],
    };

    // 画布容器样式
    const containerStyle = computed(() => ({
        backgroundImage: `radial-gradient(#d0d3d9 ${1 * scale.value}px, transparent ${1 * scale.value}px)`,
        backgroundSize: `${20 * scale.value}px ${20 * scale.value}px`,
        cursor: 'grab',
    }));

    // 内容区域样式
    const contentStyle = computed(() => ({
        transform: `scale(${scale.value}) translate(${contentPosition.value.x}px, ${contentPosition.value.y}px)`,
    }));

    // 长按缩放定时器
    let zoomTimer: ReturnType<typeof setTimeout> | null = null;

    // 长按缩放间隔
    let zoomInterval = 300;

    // 初始化标尺
    const initRulers = (): void => {
        // 使用 ResizeObserver 确保元素尺寸已确定
        const checkAndInit = () => {
            if (horizontalRef.value && verticalRef.value) {
                const hRect = horizontalRef.value.getBoundingClientRect();
                const vRect = verticalRef.value.getBoundingClientRect();

                // 确保元素有实际尺寸
                if (hRect.width > 0 && hRect.height > 0 && vRect.width > 0 && vRect.height > 0) {
                    rulers.horizontal.value = new Guides(horizontalRef.value, {
                        ...rulerConfig,
                        type: 'horizontal',
                        zoom: scale.value,
                        rulerStyle: {
                            left: '30px',
                            width: 'calc(100% - 30px)',
                            height: '100%',
                        },
                    });

                    rulers.vertical.value = new Guides(verticalRef.value, {
                        ...rulerConfig,
                        type: 'vertical',
                        zoom: scale.value,
                        rulerStyle: {
                            top: '30px',
                            height: 'calc(100% - 30px)',
                            width: '100%',
                        },
                    });

                    // 标记标尺就绪
                    rulersReady.value = true;
                    return true;
                }
            }
            return false;
        };

        // 立即检查一次
        if (!checkAndInit()) {
            // 如果没准备好，使用 requestAnimationFrame 重试
            const tryInit = () => {
                if (!checkAndInit()) {
                    requestAnimationFrame(tryInit);
                }
            };
            requestAnimationFrame(tryInit);
        }
    };

    // 更新标尺缩放
    const updateRulerZoom = (): void => {
        if (rulersReady.value) {
            rulers.horizontal.value?.setState({ zoom: scale.value });
            rulers.vertical.value?.setState({ zoom: scale.value });
        }
    };

    // 长按缩放递增
    const zoomLongPress = (direction: ZoomDirection) => {
        if ((direction === 'in' && scale.value >= MAX_SCALE.value) || (direction === 'out' && scale.value <= MIN_SCALE.value)) return;
        zoom(direction);
        zoomInterval = Math.max(50, zoomInterval - 30);
        zoomTimer = setTimeout(() => zoomLongPress(direction), zoomInterval);
    };

    // 清除长按缩放
    const clearZoomLongPress = () => {
        if (zoomTimer) clearTimeout(zoomTimer);
        zoomTimer = null;
        zoomInterval = 300;
    };

    // 缩放方法
    const zoom = (direction: ZoomDirection, amount: number = ZOOM_STEP.value): void => {
        const delta = direction === 'in' ? amount : -amount;
        const newScale = Math.min(Math.max(scale.value + delta, MIN_SCALE.value), MAX_SCALE.value);
        scale.value = Number(newScale.toFixed(2));
    };

    // 获取实际内容高度
    const getActualContentHeight = () => {
        if (!contentRef.value) return 0;

        // 获取内容区域的实际高度（包括所有子元素）
        const children = contentRef.value.children;
        if (children.length === 0) return 0;

        let maxBottom = 0;
        for (let i = 0; i < children.length; i++) {
            const child = children[i] as HTMLElement;
            const rect = child.getBoundingClientRect();
            const contentRect = contentRef.value.getBoundingClientRect();
            const relativeBottom = (rect.bottom - contentRect.top) / scale.value;
            maxBottom = Math.max(maxBottom, relativeBottom);
        }

        return maxBottom;
    };

    // 计算 y 轴滚动边界
    const calculateYBounds = () => {
        if (!containerRef.value) {
            return { minY: 0, maxY: 0 };
        }

        const containerHeight = containerRef.value.clientHeight;

        // 优先使用实际内容高度，其次使用传入的 contentHeight
        let contentHeight = getActualContentHeight();
        if (contentHeight === 0 && getContentHeight.value) {
            contentHeight = getContentHeight.value;
        }

        // 如果没有内容高度，则不限制下边界
        if (contentHeight === 0) {
            return { minY: 0, maxY: Infinity };
        }

        const scaledContentHeight = contentHeight * scale.value;

        // 上边界：不能向上滚动超过 0
        const minY = 0;
        // 下边界：当内容高度大于容器高度时，限制向下滚动
        const maxY = Math.max(0, scaledContentHeight - containerHeight);

        return { minY, maxY };
    };

    // 更新内容位置并同步标尺
    const updatePositionAndRulers = (x: number, y: number) => {
        // 限制 y 轴滚动边界
        const { minY, maxY } = calculateYBounds();
        let limitedY = y;

        // 限制上边界（不能向上滚动超过 0）
        limitedY = Math.min(limitedY, -minY);

        // 限制下边界（如果有设置 contentHeight）
        if (maxY !== Infinity) {
            limitedY = Math.max(limitedY, -maxY);
        }

        if (contentPosition.value.x === x && contentPosition.value.y === limitedY) {
            return;
        }

        contentPosition.value = { x, y: limitedY };

        // 只在标尺就绪时才同步滚动
        if (rulersReady.value) {
            nextTick(() => {
                rulers.horizontal.value?.scroll(-x);
                rulers.vertical.value?.scroll(-limitedY);
            });
        }
    };

    // 滚轮移动
    const handleWheel = (e: WheelEvent): void => {
        e.preventDefault();

        const scrollAmount = e.deltaY;
        const newY = contentPosition.value.y - scrollAmount;

        // 使用边界计算限制 y 轴滚动
        const { minY, maxY } = calculateYBounds();
        let limitedY = newY;

        // 限制上边界（不能向上滚动超过 0）
        limitedY = Math.min(limitedY, -minY);

        // 限制下边界（如果有设置 contentHeight）
        if (maxY !== Infinity) {
            limitedY = Math.max(limitedY, -maxY);
        }

        if (contentPosition.value.y === limitedY) {
            return;
        }

        contentPosition.value = { x: contentPosition.value.x, y: limitedY };

        // 只在标尺就绪时才同步滚动
        if (rulersReady.value) {
            nextTick(() => {
                rulers.horizontal.value?.scroll(-contentPosition.value.x);
                rulers.vertical.value?.scroll(-contentPosition.value.y);
            });
        }
    };

    // 计算缩放
    const calcScale = (): number => {
        const cw = getCenterWidth.value;
        const contentW = getContentWidth.value;
        if (!contentW) return 1;
        return Math.min(1, cw / contentW);
    };

    // 改变 scale
    const handleScaleChange = (): void => {
        const newScale = calcScale();
        if (newScale !== scale.value) {
            scale.value = newScale;
        }
    };

    // 绑定事件
    const bindEventListener = (): void => {
        window.addEventListener('resize', handleScaleChange);

        if (containerRef.value) {
            containerRef.value.addEventListener('wheel', handleWheel, { passive: false });
        }
    };

    // 移除事件
    const unBindEventListener = (): void => {
        window.removeEventListener('resize', handleScaleChange);

        if (containerRef.value) {
            containerRef.value.removeEventListener('wheel', handleWheel);
        }
    };

    // 重置视图
    const resetView = () => {
        // 重置缩放
        handleScaleChange();

        // 重置内容与标尺位置
        updatePositionAndRulers(0, 0);
    };

    // 监听 centerWidth 变化同步缩放
    watch([getCenterWidth, getContentWidth], () => {
        handleScaleChange();
    });

    // 监听 scale 变化同步标尺缩放
    watch(scale, () => {
        updateRulerZoom();
        // 缩放变化时重新检查滚动边界
        const { minY, maxY } = calculateYBounds();
        let limitedY = contentPosition.value.y;

        // 限制上边界
        limitedY = Math.min(limitedY, -minY);

        // 限制下边界（如果有设置 contentHeight）
        if (maxY !== Infinity) {
            limitedY = Math.max(limitedY, -maxY);
        }

        if (limitedY !== contentPosition.value.y) {
            updatePositionAndRulers(contentPosition.value.x, limitedY);
        }
    });

    // 监听标尺就绪状态，补偿之前的位置更新
    watch(rulersReady, (ready) => {
        if (ready && (contentPosition.value.x !== 0 || contentPosition.value.y !== 0)) {
            nextTick(() => {
                rulers.horizontal.value?.scroll(-contentPosition.value.x);
                rulers.vertical.value?.scroll(-contentPosition.value.y);
            });
        }
    });

    // 组件挂载时初始化
    onMounted(() => {
        // 确保 DOM 元素已挂载
        nextTick(() => {
            // 初始化标尺
            initRulers();

            // 初始化 window 事件
            bindEventListener();

            // 处理 scale 缩放
            handleScaleChange();
        });
    });

    // 组件卸载时清理事件
    onBeforeUnmount(() => {
        unBindEventListener();
    });

    // 导出 updatePositionAndRulers
    defineExpose({
        updatePositionAndRulers,
    });
</script>
