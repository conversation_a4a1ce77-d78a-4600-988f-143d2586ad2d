import { defineStore } from 'pinia';
import { reactive } from 'vue';
import { SectionSearchForm, PaginationParams, Section } from '@/types';
import { sectionApi } from '@/utils/http/register';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import { useCurrentSiteStore } from '@/stores';

// 楼层定义列表存储
export const useSectionListStore = defineStore('section-list-store', () => {
    // 状态
    const state = reactive<{
        sectionMap: Map<string, Section>,
    }>({
        sectionMap: new Map(),
    });

    // pinia store
    const currentSiteStore = useCurrentSiteStore();

    // 根据查询条件获取楼层定义列表
    const getSectionListByParams = async (params: Partial<SectionSearchForm>, pageInfo: PaginationParams) => {
        params.siteCode = currentSiteStore.currentSiteCode;
        params.delFlag = 0;
        params.status = 1;
        const sectionListRes = await sectionApi.getSections(params, pageInfo);
        if (sectionListRes.code === 200) {
            // 合并已有的
            sectionListRes.result.forEach((section: Section) => {
                state.sectionMap.set(section.code, section);
            });
        }
        return sectionListRes;
    };

    return {
        state,
        getSectionListByParams,
    };
}, {
    persist: {
        enabled: false,
        storeName: 'design-portal-store',
        driver: STORAGE_DRIVER.INDEXED_DB,
        storeKey: 'section-list-store',
    },
});
