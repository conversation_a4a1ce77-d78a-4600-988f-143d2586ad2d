<template>
    <div v-if="sectionDesignerStore.currentCell" class="flex flex-col h-full">
        <div class="w-full">
            <el-scrollbar height="100%">
                <collapse-wrapper v-model="activeNames" :accordion="true" ref="containerRef">
                    <collapse-item-wrapper id="attrs" name="attrs" title="属性" v-if="metaInfo.attrs">
                        <base-editor
                            :disabled="!permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {type: 'org', value: sectionDesignerStore.section.orgId})"
                            :metaInfos="metaInfo.attrs" v-model:values="props" @change="handleChange" />
                    </collapse-item-wrapper>
                    <collapse-item-wrapper id="controls" name="controls" title="控制" v-if="metaInfo.controls">
                        <base-editor
                            :disabled="!permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {type: 'org', value: sectionDesignerStore.section.orgId})"
                            :metaInfos="metaInfo.controls" v-model:values="props" @change="handleChange" />
                    </collapse-item-wrapper>
                    <collapse-item-wrapper id="elements" name="elements" title="子元素" v-if="metaInfo.elements">
                        <div v-if="metaInfo.elements">
                            <template v-for="element in metaInfo.elements" :key="element.name">
                                <base-editor
                                    :disabled="!permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {type: 'org', value: sectionDesignerStore.section.orgId})"
                                    :metaInfos="element" v-model:values="props" @change="handleElementChange" />
                            </template>
                        </div>
                    </collapse-item-wrapper>
                </collapse-wrapper>
            </el-scrollbar>
        </div>
    </div>

    <div v-else class="text-center text-gray-400">
        <el-empty description="请选择任意坑位" :image-size="60" />
    </div>
</template>

<script setup lang="ts">
    import { computed, ref, watch } from 'vue';
    import BaseEditor from '@/pages/editor/base-editor.vue';
    import { useSectionDesignerStore } from '@/stores';
    import { componentMetaResolver } from '@/mock/meta';
    import { DESIGN_BIZ_PERMISSION } from '@/utils/permission-list.ts';
    import { usePermissionStore } from '@chances/portal_common_core';

    // 坑位样式编辑器
    defineOptions({
        name: 'CellPropsEditor',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const permissionStore = usePermissionStore();

    const activeNames = ref<string[]>(['attrs']);

    // 当前选中的坑位
    const currentCell = computed(() => {
        if (sectionDesignerStore.currentCellIndex === -1) return null;
        return sectionDesignerStore.currentCell;
    });

    // 组件类型信息
    const metaInfo = computed(() => {
        return componentMetaResolver(currentCell.value?.component || '');
    });

    // 样式
    const props = ref<Record<string, any>>({});

    // 容器引用
    const containerRef = ref<HTMLElement | null>(null);

    // 属性变更处理
    const handleChange = () => {
        if (sectionDesignerStore.currentCellIndex !== -1 && currentCell.value) {
            // 确保 props 存在
            if (!currentCell.value.layout.props) {
                currentCell.value.layout.props = {};
            }

            // 更新所有属性
            currentCell.value.layout.props = {
                ...currentCell.value.layout.props,
                ...props.value
            };
        }
    };

    // 子元素属性变更处理
    const handleElementChange = () => {
        handleChange();
    };

    // 监听当前坑位变化，更新属性
    watch(currentCell, (newValue) => {
        if (newValue) {
            props.value = newValue.layout?.props || {};
        } else {
            props.value = {};
        }
    }, { immediate: true });

    defineExpose({
        props,
    });
</script>
