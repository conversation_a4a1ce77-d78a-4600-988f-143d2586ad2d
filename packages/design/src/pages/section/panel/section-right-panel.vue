<template>
    <el-tabs v-model="activeTab" class="p-2">
        <el-tab-pane label="楼层布局" name="sectionLayout">
            <section-layout-editor style="height: 100%; overflow: auto" />
        </el-tab-pane>

        <el-tab-pane label="坑位布局" name="cellLayout">
            <cell-layout-editor style="height: 100%; overflow: auto" />
        </el-tab-pane>

        <el-tab-pane label="坑位样式" name="cellStyle">
            <cell-props-editor style="height: 100%; overflow: auto" />
        </el-tab-pane>
    </el-tabs>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import SectionLayoutEditor from '../editor/section-layout-editor.vue';
    import CellLayoutEditor from '../editor/cell-layout-editor.vue';
    import CellPropsEditor from '../editor/cell-props-editor.vue';
    import { useSectionDesignerStore } from '@/stores';

    // 楼层定义设计器右侧面板
    defineOptions({
        name: 'SectionRightPanel',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();

    // 当前选中的tab
    const activeTab = ref('sectionLayout');

    // 监听当前选中的坑位
    watch(() => sectionDesignerStore.currentCellIndex, (newVal) => {
        if (newVal === -1) {
            // 切换到楼层布局
            activeTab.value = 'sectionLayout';
        } else {
            // 切换到坑位布局
            activeTab.value = 'cellLayout';
        }
    });
</script>
