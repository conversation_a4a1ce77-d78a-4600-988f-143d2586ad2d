<template>
    <section-toolbar-editor class="py-3 border-b border-gray-200" />

    <base-canvas
        canvas-height="calc(100% - 50px)"
        :content-width="sectionDesignerStore.sectionLayout.layout.rect.width"
        :content-height="sectionDesignerStore.sectionLayout.layout.rect.height"
    >
        <template #overlay>
            <section-canvas-editor ref="sectionCanvasEditorRef" />
        </template>
    </base-canvas>
</template>

<script setup lang="ts">
    import SectionToolbarEditor from '../editor/section-toolbar-editor.vue';
    import SectionCanvasEditor from '../editor/section-canvas-editor.vue';
    import { useSectionDesignerStore } from '@/stores';
    import { ref } from 'vue';

    // 楼层定义设计器中心面板
    defineOptions({
        name: 'SectionCenterPanel',
    })

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();

    // ref
    const sectionCanvasEditorRef = ref<HTMLElement | null>(null);

    // 暴露方法给父组件
    defineExpose({
        sectionCanvasEditorRef,
    });
</script>
