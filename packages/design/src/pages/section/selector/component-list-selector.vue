<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-2 border-b border-gray-200">
            <el-input v-model="searchQuery" placeholder="搜索组件" class="w-full" size="default" clearable>
                <template #prefix>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>

        <div class="flex-1 overflow-y-auto">
            <collapse-wrapper v-model="activeCollapseItems">
                <collapse-item-wrapper
                    v-for="(group, catalog) in filteredComponentGroups"
                    :key="catalog"
                    :name="catalog"
                    :title="`${getCatalogDisplayName(catalog)} (${group.length})`"
                >
                    <div class="grid grid-cols-2 gap-3 p-3">
                        <div
                            v-for="component in group"
                            :key="component.code"
                            class="relative border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-all bg-white hover:border-blue-300"
                            @mouseenter="(event) => onComponentHover(component, event)"
                            @mouseleave="onComponentLeave"
                        >
                            <div class="p-3 text-center">
                                <div
                                    class="w-12 h-12 mx-auto mb-2 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <el-image :src="component.icon" fit="cover"
                                              class="w-full h-full object-cover cursor-pointer"
                                              :preview-src-list="[component.icon]"
                                              hide-on-click-modal preview-teleported :z-index="3000">
                                        <template #error>
                                            <image-error-fallback text="图片损坏" />
                                        </template>
                                    </el-image>
                                </div>
                                <div class="text-sm font-medium text-gray-700 truncate" :title="component.name">
                                    {{ component.name }}
                                </div>
                            </div>
                        </div>
                    </div>
                </collapse-item-wrapper>
            </collapse-wrapper>
        </div>

        <div
            v-if="hoveredComponent && hoveredComponentStyles.length > 0"
            class="fixed bg-white border border-gray-300 rounded-lg shadow-lg p-3 z-50 w-80 max-h-80"
            :style="hoverPanelStyle"
            @mouseenter="onPanelMouseEnter"
            @mouseleave="onPanelMouseLeave"
        >
            <div class="text-sm font-medium text-gray-700 mb-2">{{ hoveredComponent.name }} 样式</div>
            <div class="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                <div
                    v-for="(style, idx) in hoveredComponentStyles"
                    :key="idx"
                    class="border border-gray-200 rounded cursor-move hover:border-blue-300 transition-all"
                    @dragover.prevent
                    draggable="true"
                    @dragstart="onDragComponentStyle($event, style)"
                >
                    <div class="relative aspect-[3/2] overflow-hidden">
                        <el-image :src="style.icon" fit="cover"
                                  class="w-full h-full object-cover cursor-pointer"
                                  :preview-src-list="[style.icon]"
                                  hide-on-click-modal preview-teleported :z-index="3000">
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </div>
                    <div class="p-1">
                        <div class="text-xs text-center truncate" :title="style.name">
                            {{ style.name }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, nextTick, onMounted, ref, watch } from 'vue';
    import { useComponentListStore } from '@/stores';
    import { Component, ComponentStyle } from '@/types';
    import { Search } from '@element-plus/icons-vue';
    import ImageErrorFallback from '@/components/image-error-fallback.vue';
    import CollapseWrapper from '@/components/collapse-wrapper.vue';
    import CollapseItemWrapper from '@/components/collapse-item-wrapper.vue';
    import { useEnumStore } from '@chances/portal_common_core';

    // 组件样式列表选择器
    defineOptions({
        name: 'ComponentListSelector',
    });

    // pinia store
    const componentListStore = useComponentListStore();
    const enumStore = useEnumStore();

    // 搜索关键词
    const searchQuery = ref('');

    // 激活的收缩面板项
    const activeCollapseItems = ref<string[]>([]);

    // 组件列表
    const componentList = ref<Component[]>([]);

    // 组件样式映射表
    const componentStylesMap = ref<Map<string, ComponentStyle[]>>(new Map());

    // 悬浮相关状态
    const hoveredComponent = ref<Component | null>(null);
    const hoverPanelStyle = ref<Record<string, string>>({});
    const isHoveringPanel = ref<boolean>(false);
    const hoverTimer = ref<number | null>(null);

    // 按 catalog 分组的组件
    const componentGroups = computed<Record<string, Component[]>>(() => {
        const groups: Record<string, Component[]> = {};

        componentList.value.forEach(component => {
            const catalog = component.catalog || '其他';
            if (!groups[catalog]) {
                groups[catalog] = [];
            }
            groups[catalog].push(component);
        });

        return groups;
    });

    // 搜索过滤后的组件分组
    const filteredComponentGroups = computed<Record<string, Component[]>>(() => {
        if (!searchQuery.value.trim()) return componentGroups.value;

        const filtered: Record<string, Component[]> = {};
        const query = searchQuery.value.toLowerCase();

        Object.entries(componentGroups.value).forEach(([catalog, components]) => {
            const filteredComponents = components.filter(component =>
                component.name.toLowerCase().includes(query) ||
                component.code.toLowerCase().includes(query),
            );

            if (filteredComponents.length > 0) {
                filtered[catalog] = filteredComponents;
            }
        });

        return filtered;
    });

    // 悬浮组件的样式列表
    const hoveredComponentStyles = computed<ComponentStyle[]>(() => {
        if (!hoveredComponent.value) return [];
        return componentStylesMap.value.get(hoveredComponent.value.code) || [];
    });

    // 获取目录显示名称
    const getCatalogDisplayName = (catalog: string): string => {
        // 从枚举中获取显示名称
        return enumStore.getLabelByKeyAndValue('componentCatalog', catalog);
    };

    // 查询组件样式
    const loadComponentStyles = async () => {
        const res = await componentListStore.getComponentStyleListByParams({ componentCodes: componentList.value.map(component => component.code) }, { paged: false });
        if (res.code === 200) {
            for (const componentStyle of res.result) {
                const arr = componentStylesMap.value.get(componentStyle.componentCode) ?? [];
                arr.push(componentStyle);
                componentStylesMap.value.set(componentStyle.componentCode, arr);
            }
        }
    };

    // 获取组件列表
    const getComponentList = async () => {
        const componentRes = await componentListStore.getComponentListByParams({}, { paged: false });
        componentList.value = componentRes.result;
    };

    // 计算悬浮面板的最佳位置
    const calculatePanelPosition = (targetRect: DOMRect) => {
        const panelWidth = 320;
        const panelHeight = 300;
        const gap = 10;

        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let left = targetRect.right + gap;
        let top = targetRect.top;

        // 水平位置调整
        if (left + panelWidth > viewportWidth) {
            // 右侧空间不足，显示在左侧
            left = targetRect.left - panelWidth - gap;

            // 如果左侧也不够，则贴着右边缘显示
            if (left < 0) {
                left = viewportWidth - panelWidth - gap;
            }
        }

        // 垂直位置调整
        if (top + panelHeight > viewportHeight) {
            // 底部空间不足，向上调整
            top = viewportHeight - panelHeight - gap;

            // 确保不超出顶部
            if (top < gap) {
                top = gap;
            }
        }

        // 确保不超出左边界
        if (left < gap) {
            left = gap;
        }

        return { left, top };
    };

    // 组件悬浮处理
    const onComponentHover = async (component: Component, event?: MouseEvent) => {
        // 清除之前的定时器
        if (hoverTimer.value !== null) {
            window.clearTimeout(hoverTimer.value);
            hoverTimer.value = null;
        }

        hoveredComponent.value = component;

        // 如果该组件的样式还没有加载，则加载
        if (!componentStylesMap.value.has(component.code)) {
            const res = await componentListStore.getComponentStyleListByParams({ componentCodes: [component.code] }, { paged: false });
            if (res.code === 200) {
                const styles = res.result.filter(style => style.category === 'base');
                componentStylesMap.value.set(component.code, styles);
            }
        }

        // 设置悬浮面板位置
        await nextTick();
        if (event) {
            const rect = (event.target as HTMLElement).getBoundingClientRect();
            const position = calculatePanelPosition(rect);
            hoverPanelStyle.value = {
                left: `${position.left}px`,
                top: `${position.top}px`,
            };
        }
    };

    // 组件离开悬浮
    const onComponentLeave = () => {
        // 延迟隐藏，给用户时间移动到悬浮面板
        hoverTimer.value = window.setTimeout(() => {
            if (!isHoveringPanel.value) {
                hoveredComponent.value = null;
                hoverPanelStyle.value = {};
            }
        }, 100);
    };

    // 悬浮面板鼠标进入
    const onPanelMouseEnter = () => {
        isHoveringPanel.value = true;
        // 清除隐藏定时器
        if (hoverTimer.value !== null) {
            window.clearTimeout(hoverTimer.value);
            hoverTimer.value = null;
        }
    };

    // 悬浮面板鼠标离开
    const onPanelMouseLeave = () => {
        isHoveringPanel.value = false;
        hoveredComponent.value = null;
        hoverPanelStyle.value = {};
    };

    // 拖拽组件样式
    const onDragComponentStyle = (event: DragEvent, componentStyle: ComponentStyle) => {
        if (!event.dataTransfer) return;
        event.dataTransfer.setData('componentStyle', JSON.stringify(componentStyle));
        event.dataTransfer.setData('application/json+componentStyle', 'componentStyle');
        event.dataTransfer.effectAllowed = 'copy';
    };

    // 监听搜索，自动展开包含搜索结果的折叠面板
    watch(() => searchQuery.value, (newQuery) => {
        if (newQuery.trim()) {
            // 搜索时展开所有包含结果的分组
            const catalogsWithResults = Object.keys(filteredComponentGroups.value);
            activeCollapseItems.value = [...catalogsWithResults];
        } else {
            // 清空搜索时，只保持第一个分组展开
            const firstCatalog = Object.keys(componentGroups.value)[0];
            if (firstCatalog) {
                activeCollapseItems.value = [firstCatalog];
            }
        }
    });

    // 组件初始化
    onMounted(async () => {
        // 查询所有组件
        await getComponentList();

        // 查询所有组件样式
        await loadComponentStyles();

        // 默认展开第一个分组
        const firstCatalog = Object.keys(componentGroups.value)[0];
        if (firstCatalog) {
            activeCollapseItems.value = [firstCatalog];
        }
    });
</script>
