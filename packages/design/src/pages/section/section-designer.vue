<template>
    <div class="h-screen w-full bg-white" v-if="isLoaded">
        <three-panel-layout :header-height="headerHeight">
            <template #top>
                <section-top-panel @save="handleSectionDesignerSave" />
            </template>

            <template #left>
                <section-left-panel />
            </template>

            <template #center>
                <section-center-panel ref="sectionCenterPanelRef" />
            </template>

            <template #right>
                <section-right-panel />
            </template>
        </three-panel-layout>
    </div>
</template>

<script setup lang="ts">
    import { nextTick, onBeforeMount, ref } from 'vue';
    import { sectionApi } from '@/utils/http/register.ts';
    import { useCurrentSiteStore, useSectionDesignerStore } from '@/stores';
    import SectionTopPanel from './panel/section-top-panel.vue';
    import SectionLeftPanel from './panel/section-left-panel.vue';
    import SectionCenterPanel from './panel/section-center-panel.vue';
    import SectionRightPanel from './panel/section-right-panel.vue';
    import { getActivePinia } from 'pinia';
    import { installAllStores } from '@chances/portal_common_core';

    // 楼层定义设计器
    defineOptions({
        name: 'SectionDesigner',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const currentSiteStore = useCurrentSiteStore();

    // 注册公共 store
    installAllStores(getActivePinia());

    // ref
    const sectionCenterPanelRef = ref<HTMLElement | null>(null);

    // 顶部高度
    const headerHeight = ref<number>(50);

    // 楼层定义编码
    const sectionCode = ref<string>('');

    // 是否加载结束
    const isLoaded = ref<boolean>(false);

    // 处理楼层定义设计器保存
    const handleSectionDesignerSave = async () => {
        // 设置当前为楼层选中
        sectionDesignerStore.selectCell(-1);
        await nextTick(() => {
        });

        // 上传楼层定义图片
        const res = await ((sectionCenterPanelRef as any)?.value?.sectionCanvasEditorRef as any)?.uploadToServer();
        if (res && res.code === 200) {
            sectionDesignerStore.section.icon = res.result;
        }

        await sectionDesignerStore.saveSection();
    };

    // 初始化
    const init = async () => {
        const res = await sectionApi.getSectionByCode(sectionCode.value);
        if (res.code === 200) {
            sectionDesignerStore.switchSection(res.result);
            currentSiteStore.switchSite(sectionDesignerStore.section.siteCode);
        } else {
            sectionDesignerStore.resetSection();
        }
    };

    // 初始化根据楼层定义编码查询楼层定义
    onBeforeMount(async () => {
        const route = (window as any).$vueRouter?.currentRoute?.value;
        sectionCode.value = route.query.sectionCode as string;
        await init();
        isLoaded.value = true;
    });
</script>

<style scoped>
    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tab-pane) {
        height: 100%;
    }
</style>
