<template>
    <div class="h-screen w-full bg-white" v-if="isLoaded">
        <three-panel-layout :header-height="headerHeight">
            <template #top>
                <page-top-panel :title="title" />
            </template>

            <template #left>
                <page-left-panel />
            </template>

            <template #center>
                <page-center-panel />
            </template>

            <template #right>
                <page-right-panel />
            </template>
        </three-panel-layout>
    </div>
</template>

<script setup lang="ts">
    import { onBeforeMount, ref } from 'vue';
    import { useCurrentSiteStore, usePageDesignerStore } from '@/stores';
    import PageTopPanel from './panel/page-top-panel.vue';
    import PageLeftPanel from './panel/page-left-panel.vue';
    import PageCenterPanel from './panel/page-center-panel.vue';
    import PageRightPanel from './panel/page-right-panel.vue';
    import { installAllStores } from '@chances/portal_common_core';
    import { getActivePinia } from 'pinia';

    // 页面设计器
    defineOptions({
        name: 'PageDesigner',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const currentSiteStore = useCurrentSiteStore();

    // 注册公共 store
    installAllStores(getActivePinia());

    // 是否加载结束
    const isLoaded = ref<boolean>(false);

    // 顶部高度
    const headerHeight = ref<number>(50);

    // 标题
    const title = ref<string>('');

    // 页面编码
    const pageCode = ref<string>('');

    // 导航编码
    const navCode = ref<string>('');

    // 桌面编码
    const desktopCode = ref<string>('');

    // 模式
    const mode = ref<string>('');

    // 初始化，获取布局和数据
    const init = async () => {
        if (mode.value === 'page') {
            title.value = '页面';
            pageDesignerStore.switchMode('page');
            pageDesignerStore.setNavAndDesktop(navCode.value, desktopCode.value);
            await pageDesignerStore.refreshPage(pageCode.value);
            currentSiteStore.switchSite(pageDesignerStore.page.siteCode);
            return;
        }

        if (mode.value === 'desktop') {
            title.value = '桌面';
            pageDesignerStore.switchMode('desktop');
            await pageDesignerStore.refreshPage(desktopCode.value);
            currentSiteStore.switchSite(pageDesignerStore.page.siteCode);
            return;
        }
    };

    // 初始化根据页面编码查询页面
    onBeforeMount(async () => {
        const route = (window as any).$vueRouter?.currentRoute?.value;
        mode.value = route?.query?.mode as string;
        desktopCode.value = route?.query?.desktopCode as string;
        navCode.value = route?.query?.navCode as string;
        pageCode.value = route?.query?.pageCode as string;

        await init();

        isLoaded.value = true;
    });
</script>

<style scoped>
    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tab-pane) {
        height: 100%;
    }
</style>
