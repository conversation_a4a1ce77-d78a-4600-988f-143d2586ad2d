<template>
    <div class="flex flex-col h-full">
        <div class="flex justify-end" style="height: 24px">
            <el-button v-if="!batchMode"
                       :disabled="!canEdit(pageDesignerStore.selectedElement) || !hasEditPermission"
                       @click="handleSaveClick" type="primary" size="small">
                保存
            </el-button>
        </div>

        <div class="w-full" style="height: calc(100% - 40px); overflow: auto">
            <el-scrollbar height="100%">
                <collapse-wrapper v-model="activeNames" :accordion="true" ref="containerRef">
                    <collapse-item-wrapper id="attrs" name="attrs" title="属性" v-if="metaInfo.attrs">
                        <base-editor
                            :disabled="!canEdit(pageDesignerStore.selectedElement) || !hasEditPermission"
                            :metaInfos="metaInfo.attrs"
                            v-model:values="props"
                            @change="handleChange" />
                    </collapse-item-wrapper>
                    <collapse-item-wrapper id="controls" name="controls" title="控制" v-if="metaInfo.controls">
                        <base-editor
                            :disabled="!canEdit(pageDesignerStore.selectedElement) || !hasEditPermission"
                            :metaInfos="metaInfo.controls"
                            v-model:values="props"
                            @change="handleChange" />
                    </collapse-item-wrapper>
                    <collapse-item-wrapper id="elements" name="elements" title="子元素" v-if="metaInfo.elements">
                        <div v-if="metaInfo.elements">
                            <template v-for="element in metaInfo.elements" :key="element.name">
                                <base-editor
                                    :disabled="!canEdit(pageDesignerStore.selectedElement) || !hasEditPermission"
                                    :metaInfos="element"
                                    v-model:values="props"
                                    @change="handleElementChange" />
                            </template>
                        </div>
                    </collapse-item-wrapper>
                </collapse-wrapper>
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref, watch } from 'vue';
    import { usePageDesignerStore } from '@/stores';
    import { componentMetaResolver } from '@/mock/meta';
    import { cellApi, pageApi, pageSectionApi } from '@/utils/http/register.ts';
    import { useFeedback } from '@/composables/use-feedback.ts';
    import { Page, PageCell, PageSection } from '@/types';
    import BaseEditor from '../../editor/base-editor.vue';
    import { canEdit } from '@/utils/publish-status-control.ts';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { ACTION, ENTITY, PERMISSION_PREFIX, SPLITTER } from '@/utils/permission-list.ts';

    // 页面设计器样式编辑器
    defineOptions({
        name: 'PagePropsEditor',
    });

    // 参数
    defineProps({
        // 批量设置模式
        batchMode: {
            type: Boolean,
            default: false,
        },
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const feedback = useFeedback();
    const permissionStore = usePermissionStore();

    // 展示项
    const activeNames = ref<string[]>(['attrs', 'controls', 'elements']);

    // 样式
    const props = ref<Record<string, any>>({});

    // 容器引用
    const containerRef = ref<HTMLElement | null>(null);

    // 元数据信息
    const metaInfo = computed(() => {
        return componentMetaResolver(pageDesignerStore.selectedElement.componentType || '');
    });

    // 是否有权限
    const hasEditPermission = computed<boolean>(() => {
        return permissionStore.hasPermission(PERMISSION_PREFIX + SPLITTER + getLowerCaseElementType() + SPLITTER + ACTION.EDIT, {
            type: 'org',
            value: pageDesignerStore.selectedElement.orgId,
        });
    });

    // 处理变化
    const handleChange = () => {
        pageDesignerStore.updateProps(props.value);
    };

    // 处理元素变化
    const handleElementChange = () => {
        pageDesignerStore.updateProps(props.value);
    };

    // 处理点击保存
    const handleSaveClick = async () => {
        pageDesignerStore.updateProps(props.value);
        let res: any;

        if (pageDesignerStore.selectedElement.componentType === 'page') {
            res = await pageApi.updatePageLayout(pageDesignerStore.selectedElement.code, pageDesignerStore.selectedElement as Page);
        } else if (pageDesignerStore.selectedElement.componentType === 'section') {
            res = await pageSectionApi.updatePageSectionLayout(pageDesignerStore.selectedElement.code, pageDesignerStore.selectedElement as PageSection);
        } else {
            res = await cellApi.updatePageCellLayout(pageDesignerStore.selectedElement.code, pageDesignerStore.selectedElement as PageCell);
        }

        if (res && res.code === 200) {
            feedback.success('更新样式成功');
        } else {
            feedback.error('更新样式失败：' + res.msg);
        }
    };

    /**
     * 获取小驼峰的元素类型
     * */
    const getLowerCaseElementType = () => {
        switch (pageDesignerStore.selectedElementType) {
            case 'Desktop':
                return ENTITY.DESKTOP;
            case 'Page':
                return ENTITY.PAGE;
            case 'PageSection':
                return ENTITY.PAGE_SECTION;
            case 'Cell':
                return ENTITY.PAGE_CELL;
        }
    };

    // 监听当前选择的元素
    watch(() => pageDesignerStore.selectedElement, () => {
        props.value = pageDesignerStore.selectedElement.layout.props || {};
    });

    defineExpose({
        props,
    });
</script>
