<template>
    <div class="p-[10px] h-full">
        <div class="flex items-center justify-between mb-4" style="height: 24px">
            <div class="w-full flex items-center justify-end">
                <action-button
                    :disabled="!canEdit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.PERMISSION_CONFIG, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                    icon="Operation" link type="primary" text-class="text-sm"
                    @click="onClickPermissionConfig" text="权限配置" />
                <action-button
                    :disabled="!canAudit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.AUDIT, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                    link type="warning" text-class="text-sm" text="送审坑位"
                    @click="onClickPublishSelf"
                >
                    <template #icon>
                        <icon icon="mdi:share" class="mr-2" />
                    </template>
                </action-button>
                <action-button
                    :disabled="!canAudit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.AUDIT_ALL, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                    link type="warning" text-class="text-sm" text="全部送审"
                    @click="onClickPublishComplete"
                >
                    <template #icon>
                        <icon icon="mdi:share-all" class="mr-2" />
                    </template>
                </action-button>
            </div>
        </div>

        <div style="height: calc(100% - 80px); overflow: auto;">
            <div class="text-red-400" v-if="pageDesignerStore.selectedPageCell.auditStatus === 0">
                当前编辑信息未送审
            </div>

            <collapse-wrapper v-model="baseInfoCollapseName" class="my-10">
                <collapse-item-wrapper name="baseInfo" title="基础信息">
                    <template #header="{isActive, toggle}">
                        <page-section-collapse-header
                            class="cursor-pointer"
                            title="基础信息"
                            :is-active="isActive"
                            :item="pageDesignerStore.selectedPageCell"
                            @click="toggle"
                        />
                    </template>

                    <el-form :rules="rules" :model="pageDesignerStore.selectedPageCell" label-width="auto"
                             label-position="left" :label-suffix="':'">
                        <el-form-item label="坑位名称" prop="name">
                            <el-input
                                :disabled="!canEdit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                                v-model="pageDesignerStore.selectedPageCell.name"
                                clearable
                            />
                        </el-form-item>
                    </el-form>
                </collapse-item-wrapper>
            </collapse-wrapper>

            <collapse-wrapper v-model="pageSectionDataCollapseName">
                <collapse-item-wrapper name="pageSectionData">
                    <template #header="{isActive, toggle}">
                        <page-section-collapse-header class="cursor-pointer" title="坑位数据源" :is-active="isActive"
                                                      :show-status="false" @click="toggle" />
                    </template>

                    <el-form label-width="auto" label-position="left" :label-suffix="':'">
                        <el-form-item label="数据源类型" prop="dsType">
                            <el-select
                                :disabled="!canEdit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                                v-model="pageDesignerStore.selectedPageCell.dsType"
                                placeholder="请选择数据源类型"
                                @change="handleDsTypeChange"
                                clearable
                            >
                                <el-option label="手动配置" value="cell"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="数据源数据条目" prop="dsParams.size">
                            <el-input-number
                                :disabled="!canEdit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                                v-model="pageDesignerStore.selectedPageCell.dsParams.size"
                                :min="1" size="default" :precision="0" style="width: 100%" />
                        </el-form-item>
                    </el-form>
                </collapse-item-wrapper>
            </collapse-wrapper>
        </div>

        <div class="flex items-center justify-end gap-2" style="height: 32px">
            <el-button
                :disabled="!canEdit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                size="default"
                @click="onClickCancelPageCellData"
            >
                取消
            </el-button>
            <el-button
                :disabled="!canEdit(pageDesignerStore.selectedPageCell) || !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {type: 'org', value: pageDesignerStore.selectedPageCell.orgId})"
                size="default" type="primary"
                @click="onClickSavePageCellData"
            >
                保存
            </el-button>
        </div>
    </div>

    <permission-config-dialog
        v-if="permissionConfigDialogVisible"
        v-model:visible="permissionConfigDialogVisible"
        :item="pageDesignerStore.selectedPageCell"
        @submit="handlePermissionConfigSubmit"
    />
</template>

<script setup lang="ts">
    import { reactive, ref } from 'vue';
    import { usePageDesignerStore } from '@/stores';
    import { FormRules } from 'element-plus';
    import { publishApi } from '@/utils/http/register.ts';
    import { useFeedback } from '@/composables';
    import PageSectionCollapseHeader from './page-section/page-section-collapse-header.vue';
    import PermissionConfigDialog from '../dialog/permission-config-dialog.vue';
    import { canAudit, canEdit } from '@/utils/publish-status-control.ts';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { DESIGN_BIZ_PERMISSION } from '@/utils/permission-list.ts';

    // 页面设计器页面坑位配置编辑器
    defineOptions({
        name: 'PageSectionConfigEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const feedback = useFeedback();
    const permissionStore = usePermissionStore();

    // 折叠面板状态
    const baseInfoCollapseName = ref<string[]>(['baseInfo']);
    const pageSectionDataCollapseName = ref<string[]>(['pageSectionData']);

    // 权限配置的对话框
    const permissionConfigDialogVisible = ref<boolean>(false);

    // 表单规则
    const rules = reactive<FormRules>({
        name: [
            { required: true, message: '请输入坑位名称', trigger: ['blur', 'change'] },
        ],
    });

    // 处理数据源类型变化
    const handleDsTypeChange = () => {
        pageDesignerStore.selectedPageCell.dsCode = '';
        pageDesignerStore.selectedPageCell.dsName = '';
    };

    // 点击权限配置
    const onClickPermissionConfig = (): void => {
        permissionConfigDialogVisible.value = true;
    };

    // 处理提交权限配置
    const handlePermissionConfigSubmit = async () => {
        const res = await pageDesignerStore.updateElement();
        if (res.code === 200) {
            permissionConfigDialogVisible.value = false;
        }
    };

    // 点击取消坑位数据
    const onClickCancelPageCellData = () => {
        pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
    };

    // 点击保存坑位数据
    const onClickSavePageCellData = () => {
        pageDesignerStore.updatePageCell(pageDesignerStore.selectedPageCell);
    };

    // 点击送审坑位
    const onClickPublishSelf = async () => {
        const res = await publishApi.publishSelf('PageCell', pageDesignerStore.selectedPageCell.code, 'CREATE');
        if (res.code === 200) {
            feedback.success('送审坑位成功');
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
        } else {
            feedback.error('送审坑位失败：' + res.msg);
        }
    };

    // 点击送审全部
    const onClickPublishComplete = async () => {
        const res = await publishApi.publishComplete('PageCell', pageDesignerStore.selectedPageCell.code, 'CREATE');
        if (res.code === 200) {
            feedback.success('送审坑位及坑位元素成功');
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
        } else {
            feedback.error('送审坑位及坑位元素失败：' + res.msg);

            // 从 msg 中提取
            const matches = [...res.msg.matchAll(/\[(.*?)\]/g)];
            const codes = matches ? matches.map(match => match[1]) : [];
            if (codes && codes.length > 1) {
                pageDesignerStore.scrollToPageSection(codes[0]);
                pageDesignerStore.switchElement('Cell', codes[1]);
            }
        }
    };
</script>

<style scoped>
    :deep(.el-divider__text) {
        padding: unset;
    }

    :deep(.el-divider__text.is-left) {
        left: 0;
    }

    .personal-collapse :deep(.el-divider__text.is-left) {
        left: 10px;
    }

    :deep(.el-collapse-item__wrap) {
        border: unset !important;
    }

    :deep(.el-collapse) {
        border: unset !important;
    }

    :deep(.el-collapse-item__header) {
        border: unset !important;
        height: auto !important;
    }

    :deep(.el-collapse-item__content) {
        padding: unset;
    }
</style>
