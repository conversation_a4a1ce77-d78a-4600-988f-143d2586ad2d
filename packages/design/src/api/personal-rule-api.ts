import { http } from '@/utils/http';
import { RestPageResultResponse } from '@chances/portal_common_core';
import { PersonalRuleSearchForm, PaginationParams, PersonalRule } from '@/types';

export interface PersonalRuleApi {
    /**
     * 分页获取推荐策略
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getPersonalRules(params: Partial<PersonalRuleSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<PersonalRule>>;
}

export class RealPersonalRuleApi implements PersonalRuleApi {
    /**
     * 分页获取推荐策略
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getPersonalRules(params: Partial<PersonalRuleSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<PersonalRule>> {
        return http.post(`/personal_rule/list`).data(params).params(pageInfo).send();
    }
}
