import { http } from '@/utils/http';
import { RestPageResultResponse } from '@chances/portal_common_core';
import { Component, ComponentStyle, ComponentSearchForm, ComponentStyleSearchForm, PaginationParams } from '@/types';

export interface ComponentApi {
    /**
     * 查询组件列表
     *
     * @param prams 查询表单
     * @param pageInfo 分页参数
     */
    getComponents(prams: Partial<ComponentSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Component>>;

    /**
     * 查询组件样式列表
     *
     * @param params 查询组件样式的参数
     * @param pageInfo 分页参数
     * */
    getComponentStyles(params: Partial<ComponentStyleSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<ComponentStyle>>;
}

export class RealComponentApi implements ComponentApi {

    /**
     * 查询组件列表
     *
     * @param prams 查询表单
     * @param pageInfo 分页参数
     */
    getComponents(prams: Partial<ComponentSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Component>> {
        return http.post(`/component/list`).params(pageInfo).data(prams).send();
    }

    /**
     * 查询组件样式列表
     *
     * @param params 查询组件样式的参数
     * @param pageInfo 分页参数
     * */
    getComponentStyles(params: Partial<ComponentStyleSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<ComponentStyle>> {
        return http.post(`/component/style/list`).params(pageInfo).data(params).send();
    }
}
