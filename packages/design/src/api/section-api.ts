import { http } from '@/utils/http';
import { SectionSearchForm, Layout, PaginationParams, Section, SectionLayoutFile } from '@/types';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';

// 楼层定义 API
export interface SectionApi {

    /**
     * 根据编码获取楼层定义
     *
     * @param code 楼层定义编码
     * */
    getSectionByCode(code: string): Promise<RestResultResponse<Section>>;

    /**
     * 分页获取楼层定义列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getSections(params: Partial<SectionSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Section>>;

    /**
     * 修改楼层定义信息
     *
     * @param code 楼层定义 code
     * @param section 楼层定义
     * */
    updateSection(code: string, section: Section): Promise<RestResultResponse<Section>>;

    /**
     * 新增、修改楼层定义布局
     *
     * @param sectionCode 楼层定义编码
     * @param layout 楼层定义布局
     * */
    updateSectionLayout(sectionCode: string, layout: SectionLayoutFile): Promise<RestResultResponse<Section>>;

    /**
     * 楼层定义另存为布局
     *
     * @param sectionCode 楼层定义编码
     * @param sectionLayoutFile 楼层定义布局文件
     * @param layoutForm 布局表单
     * */
    saveSectionAsLayout(sectionCode: string, sectionLayoutFile: SectionLayoutFile, layoutForm: Partial<Layout>): Promise<RestResultResponse<Layout>>;
}

export class RealSectionApi implements SectionApi {

    /**
     * 根据编码获取楼层定义
     *
     * @param code 楼层定义编码
     * */
    getSectionByCode(code: string): Promise<RestResultResponse<Section>> {
        return http.get(`/section`).params({ code }).send();
    }

    /**
     * 分页获取楼层定义列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getSections(params: Partial<SectionSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Section>> {
        return http.post(`/section/list`).data(params).params(pageInfo).send();
    }

    /**
     * 修改楼层定义信息
     *
     * @param code 楼层定义编码
     * @param section 楼层定义
     * */
    updateSection(code: string, section: Section): Promise<RestResultResponse<Section>> {
        return http.post(`/section/${code}`, section).send();
    }

    /**
     * 新增、修改楼层定义布局
     *
     * @param sectionCode 楼层定义编码
     * @param layout 楼层定义布局
     * */
    updateSectionLayout(sectionCode: string, layout: SectionLayoutFile): Promise<RestResultResponse<Section>> {
        return http.post(`/section/${sectionCode}/layout`, layout).send();
    }

    /**
     * 楼层定义另存为布局
     *
     * @param sectionCode 楼层定义编码
     * @param sectionLayoutFile 楼层定义布局文件
     * @param layoutForm 布局表单
     * */
    saveSectionAsLayout(sectionCode: string, sectionLayoutFile: SectionLayoutFile, layoutForm: Partial<Layout>): Promise<RestResultResponse<Layout>> {
        return http.post(`/section/${sectionCode}/save_as_layout`, sectionLayoutFile).params(layoutForm).send();
    }
}
