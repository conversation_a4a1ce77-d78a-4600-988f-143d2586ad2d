import { http } from '@/utils/http';
import { RestPageResultResponse } from '@chances/portal_common_core';
import { LinkTypeSearchForm, PaginationParams, LinkType } from '@/types';

export interface LinkTypeApi {
    /**
     * 分页获取路由配置
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLinkTypes(params: Partial<LinkTypeSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<LinkType>>;
}

export class RealLinkTypeApi implements LinkTypeApi {
    /**
     * 分页获取路由配置
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLinkTypes(params: Partial<LinkTypeSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<LinkType>> {
        return http.post(`/link_type/list`).data(params).params(pageInfo).send();
    }
}
