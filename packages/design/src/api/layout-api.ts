import { http } from '@/utils/http';
import { RestPageResultResponse, RestResultResponse } from '@chances/portal_common_core';
import { LayoutSearchForm, Layout, PaginationParams, SectionLayoutLayoutFile } from '@/types';

export interface LayoutApi {
    /**
     * 根据编码获取布局
     * */
    getLayoutByCode(code: string): Promise<RestResultResponse<Layout>>;

    /**
     * 更新布局
     * */
    updateLayout(code: string, data: Layout): Promise<RestResultResponse<Layout>>;

    /**
     * 更新布局文件
     * */
    updateLayoutFile(code: string, data: SectionLayoutLayoutFile): Promise<RestResultResponse<Layout>>;

    /**
     * 分页获取布局列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLayouts(params: Partial<LayoutSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Layout>>;
}

export class RealLayoutApi implements LayoutApi {

    /**
     * 根据编码获取布局
     * */
    getLayoutByCode(code: string): Promise<RestResultResponse<Layout>> {
        return http.get(`/layout`).params({ code }).send();
    }

    /**
     * 更新布局管理信息
     * */
    updateLayout(code: string, data: Layout): Promise<RestResultResponse<Layout>> {
        return http.post(`/layout/${code}`).data(data).send();
    }

    /**
     * 更新布局文件
     * */
    updateLayoutFile(code: string, data: SectionLayoutLayoutFile): Promise<RestResultResponse<Layout>> {
        return http.post(`/layout/${code}/file`).data(data).send();
    }

    /**
     * 分页获取布局列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLayouts(params: Partial<LayoutSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Layout>> {
        return http.post(`/layout/list`).data(params).params(pageInfo).send();
    }
}
