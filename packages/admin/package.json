{"name": "@smartdesk/admin", "versionPrefix": "1.0.", "version": "1.0.20250625164813", "author": "chances", "files": ["dist"], "type": "module", "types": "./dist/index.d.ts", "module": "./dist/@smartdesk/admin.es.js", "exports": {".": {"import": "./dist/@smartdesk/admin.es.js"}, "./dist/style.css": "./dist/style.css"}, "scripts": {"dev:dev": "vite --mode development", "dev:test": "vite --mode test", "dev:prod": "vite --mode production", "build:dev": "vue-tsc --build && vite build --mode development", "build:test": "vue-tsc --build && vite build --mode test", "build:prod": "vue-tsc --build && vite build --mode production", "build:lib:dev": "vue-tsc --build && vite build --mode development --config vite.lib.config.ts", "build:lib:test": "vue-tsc --build && vite build --mode test --config vite.lib.config.ts", "build:lib:prod": "vue-tsc --build && vite build --mode production --config vite.lib.config.ts", "type-check": "vue-tsc --build", "type-check:watch": "vue-tsc --build --watch", "test": "vitest --config vitest.config.ts"}, "dependencies": {"@smartdesk/design": "workspace:*", "@element-plus/icons-vue": "^2.3.1", "@iconify/vue": "^4.3.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "element-plus": "^2.9.10", "mitt": "^3.0.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.21", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}, "peerDependencies": {"@chances/portal_common_core": "1.0.20250619141115", "pinia": "2.3.1", "vue": "3.5.13", "vue-router": "4.5.0"}}