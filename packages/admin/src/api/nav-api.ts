import { http } from '@/utils/http';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { Nav, NavSearchForm, PaginationParams } from '@/types';

// 导航相关 API 接口
export interface NavApi {
    /**
     * 条件查询导航树
     *
     * @param searchForm 导航查询表单
     * @return 导航树
     * */
    findNavTree(searchForm: Partial<NavSearchForm>): Promise<RestResultResponse<Array<Nav>>>;

    /**
     * 条件查询导航列表
     *
     * @param searchForm 导航查询表单
     * @param pageInfo 分页参数
     * @return 导航列表
     * */
    findNavList(searchForm: Partial<NavSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Nav>>;

    /**
     * 新增导航
     *
     * @param form 导航表单
     * @return 导航
     * */
    createNav(form: Partial<Nav>): Promise<RestResultResponse<Nav>>;

    /**
     * 修改导航
     *
     * @param code 导航编码
     * @param form 导航表单
     * @return 导航
     * */
    updateNav(code: string, form: Partial<Nav>): Promise<RestResultResponse<Nav>>;

    /**
     * 启用导航
     *
     * @param code 导航编码
     * @return 启用结果
     * */
    enableNav(code: string): Promise<RestResponse>;

    /**
     * 禁用导航
     *
     * @param code 导航编码
     * @return 禁用结果
     * */
    disableNav(code: string): Promise<RestResponse>;

    /**
     * 批量启用导航
     *
     * @param codes 导航编码列表
     * @return 批量启用结果
     * */
    batchEnableNav(codes: string[]): Promise<RestResponse>;

    /**
     * 批量禁用导航
     *
     * @param codes 导航编码列表
     * @return 批量禁用结果
     * */
    batchDisableNav(codes: string[]): Promise<RestResponse>;
}

export const navApi: NavApi = {
    /**
     * 条件查询导航树
     *
     * @param searchForm 导航查询表单
     * @return 导航树
     * */
    findNavTree(searchForm: Partial<NavSearchForm>): Promise<RestResultResponse<Array<Nav>>> {
        return http.post('/nav/tree').data(searchForm).send();
    },

    /**
     * 条件查询导航列表
     *
     * @param searchForm 导航查询表单
     * @param pageInfo 分页参数
     * @return 导航列表
     * */
    findNavList(searchForm: Partial<NavSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Nav>> {
        return http.post('/nav/list').data(searchForm).params(pageInfo).send();
    },

    /**
     * 新增导航
     *
     * @param form 导航表单
     * @return 导航
     * */
    createNav(form: Partial<Nav>): Promise<RestResultResponse<Nav>> {
        return http.post("/nav", form).send();
    },

    /**
     * 修改导航
     *
     * @param code 导航编码
     * @param form 导航表单
     * @return 导航
     * */
    updateNav(code: string, form: Partial<Nav>): Promise<RestResultResponse<Nav>> {
        return http.post(`/nav/${code}`, form).send();
    },

    /**
     * 启用导航
     *
     * @param code 导航编码
     * @return 启用结果
     * */
    enableNav(code: string): Promise<RestResponse> {
        return http.post(`/nav/${code}/enable`).send();
    },

    /**
     * 禁用导航
     *
     * @param code 导航编码
     * @return 禁用结果
     * */
    disableNav(code: string): Promise<RestResponse> {
        return http.post(`/nav/${code}/disable`).send();
    },

    /**
     * 批量启用导航
     *
     * @param codes 导航编码列表
     * @return 批量启用结果
     * */
    batchEnableNav(codes: string[]): Promise<RestResponse> {
        return http.post(`/nav/batch/enable`, codes).send();
    },

    /**
     * 批量禁用导航
     *
     * @param codes 导航编码列表
     * @return 批量禁用结果
     * */
    batchDisableNav(codes: string[]): Promise<RestResponse> {
        return http.post(`/nav/batch/disable`, codes).send();
    }

}
