import { http } from '@/utils/http';
import { RestResponse } from '@chances/portal_common_core';

// 实体类型
export type EntityType = 'Desktop' | 'Nav' | 'NavGroup' | 'Page' | 'PageSection' | 'PageCell' | 'PageCellItem';

// 操作类型
export type ActionType = 'CREATE' | 'UPDATE' | 'DELETE' | 'ONLINE' | 'OFFLINE' | 'ENABLE' | 'DISABLE' | 'SORT';


export interface PublishApi {
  // 发布
  publish(entity: EntityType, code: string, action: ActionType): Promise<RestResponse>;

  // 发布全部
  publishAll(entity: EntityType, code: string, action: ActionType): Promise<RestResponse>;

  // 批量发布
  batchPublish(entity: EntityType, action: ActionType, data: any): Promise<RestResponse>;

  // 批量发布全部
  batchPublishAll(entity: EntityType, action: ActionType, data: any): Promise<RestResponse>;
}

export const publishApi: PublishApi = {
  // 发布
  publish(entity: EntityType, code: string, action: ActionType): Promise<RestResponse> {
      return http.post(`/publish/self/${entity}/${code}/${action}`).send();
  },

  // 发布全部
  publishAll(entity: EntityType, code: string, action: ActionType): Promise<RestResponse> {
    return http.post(`/publish/complete/${entity}/${code}/${action}`).send();
  },

  // 批量发布
  batchPublish(entity: EntityType, action: ActionType, data: any): Promise<RestResponse> {
    return http.post(`/publish/batch/self/${entity}/${action}`,data).send();
  },

  // 批量发布全部
  batchPublishAll(entity: EntityType, action: ActionType, data: any): Promise<RestResponse> {
    return http.post(`/publish/batch/complete/${entity}/${action}`,data).send();
  }
}
