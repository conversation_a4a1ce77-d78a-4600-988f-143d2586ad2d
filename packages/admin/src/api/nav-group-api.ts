import { http } from '@/utils/http';
import { RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { NavGroup, NavGroupSearchForm } from '@/types';

// 导航分组相关 API 接口
export interface NavGroupApi {
    /**
     * 条件查询导航分组列表
     *
     * @param searchForm 导航分组查询表单
     * @return 导航分组列表
     * */
    findNavGroupList(searchForm: Partial<NavGroupSearchForm>): Promise<RestResultResponse<Array<NavGroup>>>;

    /**
     * 新增导航分组
     *
     * @param form 导航分组表单
     * @return 导航分组
     * */
    createNavGroup(form: Partial<NavGroup>): Promise<RestResultResponse<NavGroup>>;

    /**
     * 修改导航分组
     *
     * @param code 导航分组编码
     * @param form 导航分组表单
     * @return 导航分组
     * */
    updateNavGroup(code: string, form: Partial<NavGroup>): Promise<RestResultResponse<NavGroup>>;

    /**
     * 启用导航分组
     *
     * @param code 导航分组编码
     * @return 启用结果
     * */
    enableNavGroup(code: string): Promise<RestResponse>;

    /**
     * 禁用导航分组
     *
     * @param code 导航分组编码
     * @return 禁用结果
     * */
    disableNavGroup(code: string): Promise<RestResponse>;

    /**
     * 批量启用导航分组
     *
     * @param codes 导航分组编码列表
     * @return 批量启用结果
     * */
    batchEnableNavGroup(codes: string[]): Promise<RestResponse>;

    /**
     * 批量禁用导航分组
     *
     * @param codes 导航分组编码列表
     * @return 批量禁用结果
     * */
    batchDisableNavGroup(codes: string[]): Promise<RestResponse>;
}

export const navGroupApi: NavGroupApi = {
    /**
     * 条件查询导航分组列表
     *
     * @param searchForm 导航分组查询表单
     * @return 导航分组列表
     * */
    findNavGroupList(searchForm: Partial<NavGroupSearchForm>): Promise<RestResultResponse<Array<NavGroup>>> {
        return http.post('/nav/group/list').data(searchForm).send();
    },

    /**
     * 新增导航分组
     *
     * @param form 导航分组表单
     * @return 导航分组
     * */
    createNavGroup(form: Partial<NavGroup>): Promise<RestResultResponse<NavGroup>> {
        return http.post('/nav/group', form).send();
    },

    /**
     * 修改导航分组
     *
     * @param code 导航分组编码
     * @param form 导航分组表单
     * @return 导航分组
     * */
    updateNavGroup(code: string, form: Partial<NavGroup>): Promise<RestResultResponse<NavGroup>> {
        return http.post(`/nav/group/${code}`, form).send();
    },

    /**
     * 启用导航分组
     *
     * @param code 导航分组编码
     * @return 启用结果
     * */
    enableNavGroup(code: string): Promise<RestResponse> {
        return http.post(`/nav/group/${code}/enable`).send();
    },

    /**
     * 禁用导航分组
     *
     * @param code 导航分组编码
     * @return 禁用结果
     * */
    disableNavGroup(code: string): Promise<RestResponse> {
        return http.post(`/nav/group/${code}/disable`).send();
    },

    /**
     * 批量启用导航分组
     *
     * @param codes 导航分组编码列表
     * @return 批量启用结果
     * */
    batchEnableNavGroup(codes: string[]): Promise<RestResponse> {
        return http.post(`/nav/group/batch/enable`, codes).send();
    },

    /**
     * 批量禁用导航分组
     *
     * @param codes 导航编码列表
     * @return 批量禁用结果
     * */
    batchDisableNavGroup(codes: string[]): Promise<RestResponse> {
        return http.post(`/nav/group/batch/disable`, codes).send();
    },

};
