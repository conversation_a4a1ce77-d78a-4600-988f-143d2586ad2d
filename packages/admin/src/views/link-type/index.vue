<template>
    <PageContainer>
        <template #search>
            <link-type-search @search="searchLinkType" />
        </template>

        <template #toolbar>
            <link-type-toolbars
                :selected-link-type="selectedLinkType"
                @add="addLinkType"
                @refresh="refreshLinkTypeList" />
        </template>

        <template #list>
            <link-type-list
                ref="linkTypeListRef"
                :search-form="searchForm"
                @edit="editLinkType"
                @update:selection="handleSelectionLinkType" />
        </template>
    </PageContainer>
    <link-type-add
        v-model:visible="addLinkTypeVisible"
        v-model:linkTypeData="currentLinkType"
        @save="saveLinkType" />
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue';
    import { LinkType, LinkTypeSearchForm } from '@/types';
    import { linkTypeApi } from '@/api';
    import LinkTypeSearch from './compoents/link-type-search.vue';
    import LinkTypeList from './compoents/link-type-list.vue';
    import LinkTypeToolbars from './compoents/link-type-toolbars.vue';
    import LinkTypeAdd from './compoents/link-type-add.vue';
    import { useFeedback } from '@/composables';
    import PageContainer from '../common/page-container.vue';

    // pinia store
    const feedback = useFeedback();

    // 跳转链接查询表单
    const searchForm = ref<Partial<LinkTypeSearchForm>>({});

    // 已选中的跳转路径列表
    const selectedLinkType = ref<LinkType[]>([]);

    // 当前的跳转链接
    const currentLinkType = ref<LinkType>({} as LinkType);

    // 新增跳转链接弹窗显隐
    const addLinkTypeVisible = ref<boolean>(false);

    // 跳转列表引用
    const linkTypeListRef = ref<InstanceType<typeof LinkTypeList> | null>(null);

    // 刷新跳转链接列表
    const refreshLinkTypeList = () => {
        linkTypeListRef.value?.getLinkTypeList();
    };

    // 搜索跳转链接
    const searchLinkType = (form: LinkTypeSearchForm) => {
        searchForm.value = { ...form };
    };

    // 中转已选中跳转链接
    const handleSelectionLinkType = (selection: LinkType[]) => {
        selectedLinkType.value = selection;
    };

    // 新增跳转链接
    const addLinkType = () => {
        currentLinkType.value = {} as LinkType;
        addLinkTypeVisible.value = true;
    };

    // 修改跳转链接
    const editLinkType = (linkType: LinkType) => {
        currentLinkType.value = linkType;
        addLinkTypeVisible.value = true;
    };

    // 保存跳转链接
    const saveLinkType = (linkType: LinkType) => {
        // 修改
        if (linkType.id) {
            updateLinkType(linkType);
        } else {
            // 新增
            createLinkType(linkType);
        }
    };

    // 更新跳转链接
    const updateLinkType = async (form: LinkType) => {
        const res = await linkTypeApi.updateLinkType(form.code, form);
        if (res.code === 200) {
            refreshLinkTypeList();
            addLinkTypeVisible.value = false;
            feedback.success('更新跳转链接成功');
        } else {
            feedback.error('更新跳转链接失败：' + res.msg);
        }
    };

    // 新增跳转链接
    const createLinkType = async (form: LinkType) => {
        const res = await linkTypeApi.createLinkType(form);
        if (res.code === 200) {
            refreshLinkTypeList();
            addLinkTypeVisible.value = false;
            feedback.success('新增跳转链接成功');
        } else {
            feedback.error('新增跳转链接失败：' + res.msg);
        }
    };

    // 组件挂载
    onMounted(() => {
        refreshLinkTypeList();
    });
</script>