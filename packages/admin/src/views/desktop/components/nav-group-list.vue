<template>
    <el-table :data="navGroupList" row-key="id"
              :header-cell-style="{ color: 'black', height: '50px' }"
              :tree-props="{ children: 'children', checkStrictly: true }"
              style="width: 100%"
              @selection-change="handleSelectionChange"
    >
        <el-table-column type="selection" width="70" label="全选" />
        <el-table-column property="title" label="导航分组名称" width="300" show-overflow-tooltip>
            <template #default="{ row }">
                {{ row.title }} {{ row.children?.length ? '（' + row.children.length + '）' : '' }}
            </template>
        </el-table-column>
        <el-table-column property="expandEffect" label="伸缩效果" width="100">
            <template #default="{ row }">
                <div>{{ enumStore.getLabelByKeyAndValue('expandEffect', row.expandEffect) }}</div>
            </template>
        </el-table-column>
        <el-table-column property="navCodes" label="导航" width="180">
            <template #default="{ row }">
                <div>{{ getNavTitles(row.navCodes) }}</div>
            </template>
        </el-table-column>
        <el-table-column property="orgId" label="组织" width="180">
            <template #default="{ row }">
                <div>{{ orgOptions.find(org => org.value === row.orgId)?.label }}</div>
            </template>
        </el-table-column>
        <el-table-column label="删除状态" width="100">
            <template #default="{ row }">
                <div>{{ enumStore.getLabelByKeyAndValue('delFlag', row.delFlag) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="可用状态" width="100">
            <template #default="{ row }">
                <el-switch
                    v-if="row.status === 1"
                    :disabled="!canDisable(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV_GROUP.DISABLE, {type: 'org', value: row.orgId})"
                    v-model="row.status"
                    size="small"
                    :active-value="1"
                    :inactive-value="0"
                    :inactive-text="row.status ? '可用' : '不可用'"
                    @change="handleStatusChange(row)"
                />
                <el-switch
                    v-else
                    :disabled="!canEnable(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV_GROUP.ENABLE, {type: 'org', value: row.orgId})"
                    v-model="row.status"
                    size="small"
                    :active-value="1"
                    :inactive-value="0"
                    :inactive-text="row.status ? '可用' : '不可用'"
                    @change="handleStatusChange(row)"
                />
            </template>
        </el-table-column>
        <el-table-column label="审核状态" width="100">
            <template #default="{ row }">
                <div>{{ enumStore.getLabelByKeyAndValue('auditStatus', row.auditStatus) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="可见状态" width="100">
            <template #default="{ row }">
                <div>{{ enumStore.getLabelByKeyAndValue('visibleStatus', row.visibleStatus) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="上线状态" width="100">
            <template #default="{ row }">
                <div>{{ enumStore.getLabelByKeyAndValue('onlineStatus', row.onlineStatus) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="修改时间" width="180">
            <template #default="{ row }">
                {{ format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') }}
            </template>
        </el-table-column>
        <el-table-column property="modifiedBy" label="修改人" width="100" />
        <el-table-column label="操作" fixed="right" width="420">
            <template #default="{ row }">
                <el-button
                    :disabled="!canEdit(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV_GROUP.EDIT, {type: 'org', value: row.orgId})"
                    type="primary"
                    text
                    @click="onClickEdit(row)"
                >
                    编辑
                </el-button>
                <el-button
                    :disabled="!canAudit(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV_GROUP.AUDIT, {type: 'org', value: row.orgId})"
                    type="primary"
                    text
                    @click="onClickPublish(row)"
                >
                    送审
                </el-button>
                <el-button
                    :disabled="!canOnline(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV_GROUP.ONLINE, {type: 'org', value: row.orgId})"
                    v-if="canOnline(row)"
                    type="primary"
                    text
                    @click="onClickOnline(row)"
                >
                    上线
                </el-button>
                <el-button
                    :disabled="!canOffline(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV_GROUP.OFFLINE, {type: 'org', value: row.orgId})"
                    v-if="canOffline(row)"
                    type="warning"
                    text
                    @click="onClickOffline(row)"
                >
                    下线
                </el-button>
                <el-button
                    :disabled="!canDelete(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV_GROUP.DELETE, {type: 'org', value: row.orgId})"
                    type="danger"
                    text
                    @click="onClickDelete(row)"
                >
                    删除
                </el-button>
            </template>
        </el-table-column>
    </el-table>
</template>

<script setup lang="ts">
    import { onMounted, PropType, ref, watch } from 'vue';
    import { Nav, NavGroup, NavGroupSearchForm } from '@/types';
    import { format } from 'date-fns';
    import { dimensionApi, navApi, navGroupApi, publishApi } from '@/api';
    import { useSiteStore } from '@/stores/site-store.ts';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canEdit,
        canEnable,
        canOffline,
        canOnline,
    } from '@/utils';
    import { useFeedback } from '@/composables';

    // 导航分组列表
    defineOptions({
        name: 'NavGroupList',
    });

    // 参数
    const props = defineProps({
        sectionForm: {
            type: Object as PropType<Partial<NavGroupSearchForm>>,
            default: () => ({}),
        },
    });

    // 事件
    const emits = defineEmits(['update:selection', 'edit']);

    // 网站 pinia
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 导航分组列表
    const navGroupList = ref<NavGroup[]>([]);

    // 选中的导航分组
    const selectedNavGroups = ref<NavGroup[]>([]);

    // 导航列表
    const navList = ref<Nav[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 获取导航名称
    const getNavTitles = (navCodes: string[]) => {
        if (!navCodes || navCodes.length === 0) {
            return '-';
        }

        const navMap = new Map(navList.value.map(nav => [nav.code, nav.title]));
        return navCodes.map(code => navMap.get(code)).filter(Boolean).join(',') || '-';
    };

    // 处理选中
    const handleSelectionChange = (val: NavGroup[]) => {
        selectedNavGroups.value = val;
        emits('update:selection', val);
    };

    // 启用/禁用导航分组
    const handleStatusChange = async (row: NavGroup) => {
        const word = row.status === 0 ? '禁用' : '启用';
        if (await feedback.confirm(`确定要${word}该导航分组吗？`, '确定操作', 'warning')) {
            const response = row.status === 0 ? await navGroupApi.disableNavGroup(row.code) : await navGroupApi.enableNavGroup(row.code);
            if (response.code === 200) {
                feedback.success(`${word}导航分组成功`);
                await getNavGroupList();
            } else {
                feedback.error(`${word}导航分组失败：` + response.msg);
            }
        } else {
            row.status = row.status === 1 ? 0 : 1;
        }
    };

    // 点击编辑
    const onClickEdit = (row: NavGroup) => {
        emits('edit', row);
    };

    // 点击删除
    const onClickDelete = async (row: NavGroup) => {
        if (await feedback.confirm(`确定要删除该导航分组吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishAll('NavGroup', row.code, 'DELETE');
            if (res.code === 200) {
                feedback.success('删除导航分组成功');
                await getNavGroupList();
            } else {
                feedback.error('删除导航分组失败：' + res.msg);
            }
        }
    };

    // 点击送审
    const onClickPublish = async (row: NavGroup) => {
        if (await feedback.confirm(`确定要送审该导航分组吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publish('NavGroup', row.code, 'CREATE');
            if (res.code === 200) {
                feedback.success('送审导航分组成功');
                await getNavGroupList();
            } else {
                feedback.error('送审导航分组失败：' + res.msg);
            }
        }
    };

    // 点击上线
    const onClickOnline = async (row: NavGroup) => {
        if (await feedback.confirm(`确定要上线该导航分组吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publish('NavGroup', row.code, 'ONLINE');
            if (res.code === 200) {
                feedback.success('上线导航分组成功');
                await getNavGroupList();
            } else {
                feedback.error('上线导航分组失败：' + res.msg);
            }
        }
    };

    // 点击下线
    const onClickOffline = async (row: NavGroup) => {
        if (await feedback.confirm(`确定要下线该导航分组吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publish('NavGroup', row.code, 'OFFLINE');
            if (res.code === 200) {
                feedback.success('下线导航分组成功');
                await getNavGroupList();
            } else {
                feedback.error('下线导航分组失败：' + res.msg);
            }
        }
    };

    // 查询导航分组列表
    const getNavGroupList = async () => {
        try {
            const response = await navGroupApi.findNavGroupList(props.sectionForm);
            if (response.code === 200) {
                navGroupList.value = response.result;
            } else {
                feedback.error('获取导航分组列表失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('获取导航分组列表失败');
        }
    };

    // 查询导航列表
    const getNavList = async () => {
        const res = await navApi.findNavList({
            siteCode: siteStore.currentSite?.code,
            desktopCode: props.sectionForm.desktopCode,
            delFlag: 0,
            status: 1,
        }, { paged: false });
        if (res.code === 200) {
            navList.value = res.result;
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    watch(() => props.sectionForm, (newVal) => {
        // 只有网站编码、桌面编码都存在时，才查询导航分组列表
        if (newVal.siteCode && newVal.desktopCode) {
            getNavGroupList();
        }
    }, { deep: true, immediate: true });

    onMounted(() => {
        getNavList();
        getOrgOptions();
    });

    defineExpose({
        getNavGroupList,
        selectedNavGroups,
    });

</script>
