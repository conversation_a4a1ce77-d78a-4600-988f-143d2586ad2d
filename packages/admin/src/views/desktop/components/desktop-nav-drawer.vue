<template>
    <el-drawer :title="desktop?.name + '导航'" v-model="visible"
               destroy-on-close direction="rtl" size="80%"
               append-to-body :z-index="1000"
    >
        <nav-search v-model="searchForm" @search="handleSearch" />

        <nav-toolbars
            :selected-navs="selectedNavs"
            @add="handleAdd"
            @refresh="handleSearch"
        />

        <nav-list ref="navListRef"
                  :section-form="searchForm"
                  @edit="handleEdit"
                  @add-child-nav="handleAddChildNav"
                  @update:selection="handleUpdateSelection"
        />
    </el-drawer>
</template>

<script setup lang="ts">
    import { computed, PropType, ref, watch } from 'vue';
    import { Nav, NavSearchForm, Page } from '@/types';
    import NavToolbars from './nav-toolbars.vue';
    import NavList from './nav-list.vue';
    import NavSearch from './nav-search.vue';
    import { useSiteStore } from '@/stores';
    import { useRouter } from 'vue-router';

    // 桌面导航抽屉
    defineOptions({
        name: 'DesktopNavDrawer',
    });

    // 参数
    const props = defineProps({
        modelValue: {
            type: Boolean,
            required: true,
        },
        desktop: {
            type: Object as PropType<Page>,
            required: true,
        },
    });

    // 事件
    const emit = defineEmits(['update:modelValue', 'save', 'addNav', 'editNav', 'addChildNav']);

    // pinia store
    const siteStore = useSiteStore();
    const router = useRouter();

    // 导航列表引用
    const navListRef = ref<HTMLElement | null>(null);

    // 被选中的导航列表
    const selectedNavs = ref<Nav[]>([]);

    // 用户是否主动展开过抽屉
    const userHasOpened = ref(false);

    // 抽屉的显隐
    const visible = computed({
        get: () => props.modelValue,
        set: (value) => emit('update:modelValue', value),
    });

    // 查询表单
    const searchForm = ref<Partial<NavSearchForm>>({
        siteCode: siteStore.currentSiteCode,
        desktopCode: props.desktop.code,
        delFlag: 0,
    });

    // 处理查询
    const handleSearch = () => {
        (navListRef.value as any)?.getNavTree();
        (navListRef.value as any)?.getPageList();
    };

    // 处理新增导航
    const handleAdd = () => {
        emit('addNav');
    };

    // 处理编辑
    const handleEdit = (data: Nav) => {
        emit('editNav', data);
    };

    // 处理新增子导航
    const handleAddChildNav = (data: Nav) => {
        emit('addChildNav', data);
    };

    // 处理多选事件
    const handleUpdateSelection = (data: Nav[]) => {
        selectedNavs.value = data;
    };

    // 监听 siteStore，导航查询表单设置网站编码
    watch(() => siteStore.currentSiteCode, (newVal) => {
        searchForm.value.siteCode = newVal;
    });

    // 监听 desktop 参数，导航查询表单设置桌面编码
    watch(() => props.desktop, (newVal) => {
        searchForm.value.desktopCode = newVal.code;
    });

    // 监听当前路由
    watch(() => router.currentRoute.value, () => {
        const isDesktopPage = router.currentRoute.value.path === '/desktop';

        if (!isDesktopPage) {
            visible.value = false;
        } else if (userHasOpened.value) {
            visible.value = true;
        }
    });

    // 监听抽屉关闭，当用户手动关闭时重置状态
    watch(() => props.modelValue, (newVal, oldVal) => {
        if (oldVal && !newVal && router.currentRoute.value.path === '/desktop') {
            userHasOpened.value = false;
        }
    });

    // 用户主动展开抽屉
    const openDrawer = () => {
        userHasOpened.value = true;
        visible.value = true;
    };

    // 用户主动关闭抽屉
    const closeDrawer = () => {
        visible.value = false;
    };

    defineExpose({
        handleSearch,
        openDrawer,
        closeDrawer,
    });

</script>
