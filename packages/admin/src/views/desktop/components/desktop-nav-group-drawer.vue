<template>
    <el-drawer :title="desktop?.name + '导航分组'" v-model="visible"
               destroy-on-close direction="rtl" size="80%"
               append-to-body :z-index="1000"
    >
        <nav-group-search v-model="searchForm" @search="handleSearch" />

        <nav-group-toolbars
            :selected-nav-groups="selectedNavGroups"
            @add="handleAdd"
            @refresh="handleSearch"
        />

        <nav-group-list
            ref="navGroupListRef"
            :section-form="searchForm"
            @edit="handleEdit"
            @update:selection="handleUpdateSelection"
        />
    </el-drawer>
</template>

<script setup lang="ts">
    import { computed, PropType, ref, watch } from 'vue';
    import { NavGroup, NavGroupSearchForm, Page } from '@/types';
    import { useSiteStore } from '@/stores';
    import { useRouter } from 'vue-router';
    import NavGroupSearch from './nav-group-search.vue';
    import NavGroupToolbars from './nav-group-toolbars.vue';
    import NavGroupList from './nav-group-list.vue';

    // 桌面导航分组抽屉
    defineOptions({
        name: 'DesktopNavGroupDrawer',
    });

    // 参数
    const props = defineProps({
        modelValue: {
            type: Boolean,
            required: true,
        },
        desktop: {
            type: Object as PropType<Page>,
            required: true,
        },
    });

    // 事件
    const emit = defineEmits(['update:modelValue', 'save', 'addNavGroup', 'editNavGroup']);

    // pinia store
    const siteStore = useSiteStore();
    const router = useRouter();

    // 导航分组列表引用
    const navGroupListRef = ref<HTMLElement | null>(null);

    // 被选中的导航列表
    const selectedNavGroups = ref<NavGroup[]>([]);

    // 用户是否主动展开过抽屉
    const userHasOpened = ref(false);

    // 抽屉的显隐
    const visible = computed({
        get: () => props.modelValue,
        set: (value) => emit('update:modelValue', value),
    });

    // 查询表单
    const searchForm = ref<Partial<NavGroupSearchForm>>({
        siteCode: siteStore.currentSiteCode,
        desktopCode: props.desktop.code,
        delFlag: 0,
    });

    // 处理查询
    const handleSearch = () => {
        (navGroupListRef.value as any)?.getNavGroupList();
    };

    // 处理新增导航
    const handleAdd = () => {
        emit('addNavGroup');
    };

    // 处理编辑
    const handleEdit = (data: NavGroup) => {
        emit('editNavGroup', data);
    };

    // 处理多选事件
    const handleUpdateSelection = (data: NavGroup[]) => {
        selectedNavGroups.value = data;
    };

    // 监听 siteStore，导航查询表单设置网站编码
    watch(() => siteStore.currentSiteCode, (newVal) => {
        searchForm.value.siteCode = newVal;
    });

    // 监听 desktop 参数，导航查询表单设置桌面编码
    watch(() => props.desktop, (newVal) => {
        searchForm.value.desktopCode = newVal.code;
    });

    // 监听当前路由
    watch(() => router.currentRoute.value, () => {
        const isDesktopPage = router.currentRoute.value.path === '/desktop';

        if (!isDesktopPage) {
            visible.value = false;
        } else if (userHasOpened.value) {
            visible.value = true;
        }
    });

    // 监听抽屉关闭，当用户手动关闭时重置状态
    watch(() => props.modelValue, (newVal, oldVal) => {
        if (oldVal && !newVal && router.currentRoute.value.path === '/desktop') {
            userHasOpened.value = false;
        }
    });

    // 用户主动展开抽屉
    const openDrawer = () => {
        userHasOpened.value = true;
        visible.value = true;
    };

    // 用户主动关闭抽屉
    const closeDrawer = () => {
        visible.value = false;
    };

    defineExpose({
        handleSearch,
        openDrawer,
        closeDrawer,
    });

</script>
