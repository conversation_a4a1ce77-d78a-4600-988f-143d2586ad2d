<template>
    <div class="flex items-start justify-between">
        <div class="flex items-center">
            <el-form inline label-suffix=":">
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlag"
                        placeholder="请选择"
                        size="default"
                        style="width: 160px"
                        clearable
                    >
                        <el-option
                            v-for="item in delFlagOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.status"
                        placeholder="请选择"
                        size="default"
                        style="width: 160px"
                        clearable
                    >
                        <el-option
                            v-for="item in enableStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="审核状态">
                    <el-select
                        v-model="searchForm.auditStatus"
                        placeholder="请选择"
                        size="default"
                        style="width: 160px"
                        clearable
                    >
                        <el-option
                            v-for="item in auditStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="可见状态">
                    <el-select
                        v-model="searchForm.visibleStatus"
                        placeholder="请选择"
                        size="default"
                        style="width: 160px"
                        clearable
                    >
                        <el-option
                            v-for="item in visibleStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="上线状态">
                    <el-select
                        v-model="searchForm.onlineStatus"
                        placeholder="请选择"
                        size="default"
                        style="width: 160px"
                        clearable
                    >
                        <el-option
                            v-for="item in onlineStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <div>
            <el-input
                style="width: 360px"
                v-model="searchForm.title"
                placeholder="请输入"
                size="default"
                clearable
            >
                <template #prefix>
                    <el-icon class="cursor-pointer" @click="emit('search')">
                        <search />
                    </el-icon>
                </template>
            </el-input>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import { NavGroupSearchForm } from '@/types';
    import { useEnumStore } from '@chances/portal_common_core';

    // 参数
    const props = defineProps<{
        modelValue: Partial<NavGroupSearchForm>
    }>();

    const emit = defineEmits<{
        // 送审当前
        (e: 'update:modelValue', item: Partial<NavGroupSearchForm>): void;
        (e: 'search'): void;
    }>();

    // pinia store
    const enumStore = useEnumStore();

    // 查询表单
    const searchForm = computed({
        get: () => props.modelValue,
        set: (value) => emit('update:modelValue', value),
    });

    // 删除状态枚举
    const delFlagOption = ref<{ name: any; code: number; }[]>(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 启用状态枚举
    const enableStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 审核状态枚举
    const auditStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('auditStatus') || []);

    // 可见状态枚举
    const visibleStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('visibleStatus') || []);

    // 上线状态枚举
    const onlineStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('onlineStatus') || []);
</script>
