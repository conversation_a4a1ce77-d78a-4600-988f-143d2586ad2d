<template>
    <div class="scroll-wrapper">
        <div
            class="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-4 h-full">
            <el-card
                v-for="(desktop, index) in desktopList"
                :key="index"
                class="card-item transition-all duration-300 ease-out hover:shadow-xl hover:-translate-y-2 ring-1 ring-gray-300 hover:ring-blue-400"
                shadow="never">
                <image-card
                    hide-on-click-modal
                    :src="desktop.icon"
                    :with-prefix="false"
                    :title="desktop.name"
                    :resolution="
                        enumStore.getLabelByKeyAndValue(
                            'resolution',
                            desktop.resolution
                        )
                    "
                    class="w-full aspect-video rounded-lg"
                    fit="cover" />

                <div class="mt-3 w-full flex flex-nowrap gap-2">
                    <div class="flex-1 min-w-[90px]">
                        <el-button
                            class="w-full truncate"
                            type="primary"
                            @click="onClickDesign(desktop)">
                            <span class="mx-2">配置</span>
                        </el-button>
                    </div>
                    <div class="flex-1 min-w-[90px]">
                        <el-button
                            class="w-full truncate"
                            type="success"
                            @click="onClickNav(desktop)">
                            <span class="mx-2">导航</span>
                        </el-button>
                    </div>
                    <div class="flex-shrink-0">
                        <el-dropdown :hide-on-click="false">
                            <el-button
                                class="w-full px-3"
                                type="info">
                                <span class="mx-2.5">更多</span>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>
                                        <el-button
                                            class="w-full"
                                            type="success"
                                            @click="onClickNavGroup(desktop)">
                                            <span class="mx-2.5">导航分组</span>
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item>
                                        <el-button
                                            :disabled="
                                                !canAudit(desktop) ||
                                                !permissionStore.hasPermission(
                                                    ADMIN_BIZ_PERMISSION.DESKTOP
                                                        .AUDIT,
                                                    {
                                                        type: 'org',
                                                        value: desktop.orgId,
                                                    }
                                                )
                                            "
                                            class="w-full"
                                            type="primary"
                                            @click="onClickPublish(desktop)">
                                            <span class="mx-2.5">送审</span>
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item>
                                        <el-button
                                            :disabled="
                                                !canDelete(desktop) ||
                                                !permissionStore.hasPermission(
                                                    ADMIN_BIZ_PERMISSION.DESKTOP
                                                        .DELETE,
                                                    {
                                                        type: 'org',
                                                        value: desktop.orgId,
                                                    }
                                                )
                                            "
                                            class="w-full"
                                            type="danger"
                                            @click="onClickDelete(desktop)">
                                            <span class="mx-2.5">删除</span>
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item v-if="canOnline(desktop)">
                                        <el-button
                                            :disabled="
                                                !canOnline(desktop) ||
                                                !permissionStore.hasPermission(
                                                    ADMIN_BIZ_PERMISSION.DESKTOP
                                                        .ONLINE,
                                                    {
                                                        type: 'org',
                                                        value: desktop.orgId,
                                                    }
                                                )
                                            "
                                            class="w-full"
                                            type="success"
                                            @click="onClickOnline(desktop)">
                                            <span class="mx-2.5">上线</span>
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="canOffline(desktop)">
                                        <el-button
                                            :disabled="
                                                !canOffline(desktop) ||
                                                !permissionStore.hasPermission(
                                                    ADMIN_BIZ_PERMISSION.DESKTOP
                                                        .OFFLINE,
                                                    {
                                                        type: 'org',
                                                        value: desktop.orgId,
                                                    }
                                                )
                                            "
                                            class="w-full"
                                            type="danger"
                                            @click="onClickOffline(desktop)">
                                            <span class="mx-2.5">下线</span>
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item v-if="canEnable(desktop)">
                                        <el-button
                                            :disabled="
                                                !canEnable(desktop) ||
                                                !permissionStore.hasPermission(
                                                    ADMIN_BIZ_PERMISSION.DESKTOP
                                                        .ENABLE,
                                                    {
                                                        type: 'org',
                                                        value: desktop.orgId,
                                                    }
                                                )
                                            "
                                            class="w-full"
                                            type="primary"
                                            @click="onClickEnable(desktop)">
                                            <span class="mx-2.5">启用</span>
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="canDisable(desktop)">
                                        <el-button
                                            :disabled="
                                                !canDisable(desktop) ||
                                                !permissionStore.hasPermission(
                                                    ADMIN_BIZ_PERMISSION.DESKTOP
                                                        .DISABLE,
                                                    {
                                                        type: 'org',
                                                        value: desktop.orgId,
                                                    }
                                                )
                                            "
                                            class="w-full"
                                            type="warning"
                                            @click="onClickDisable(desktop)">
                                            <span class="mx-2.5">禁用</span>
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item>
                                        <el-button
                                            :disabled="
                                                !canEdit(desktop) ||
                                                !permissionStore.hasPermission(
                                                    ADMIN_BIZ_PERMISSION.DESKTOP
                                                        .EDIT,
                                                    {
                                                        type: 'org',
                                                        value: desktop.orgId,
                                                    }
                                                )
                                            "
                                            class="w-full"
                                            type="warning"
                                            @click="onClickEdit(desktop)">
                                            <span class="mx-2.5">编辑</span>
                                        </el-button>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </el-card>

            <el-card
                v-if="
                    permissionStore.hasPermission(
                        ADMIN_BIZ_PERMISSION.DESKTOP.CREATE
                    )
                "
                class="card-item transition-all duration-300 ease-out hover:shadow-xl hover:-translate-y-2 ring-1 ring-gray-300 hover:ring-blue-400"
                shadow="never"
                @click="onClickAdd">
                <div class="h-full flex items-center justify-center">
                    <el-icon
                        size="24"
                        color="gray">
                        <Plus />
                    </el-icon>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">


    import { PropType } from 'vue';
    import { Page } from '@/types';
    import { Plus } from '@element-plus/icons-vue';
    import { useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canEdit,
        canEnable,
        canOffline,
        canOnline,
    } from '@/utils';

    // 参数
    const props = defineProps({
        desktopList: {
            type: Array as PropType<Page[]>,
            required: true,
        },
    });

    // 事件
    const emit = defineEmits(['design', 'publish', 'edit', 'nav', 'navGroup', 'delete', 'add', 'online', 'offline', 'enable', 'disable']);

    // pinia store
    const permissionStore = usePermissionStore();
    const enumStore = useEnumStore();

    // 配置：跳转桌面设计器
    const onClickDesign = (desktop: Page) => emit('design', desktop);

    // 送审
    const onClickPublish = (desktop: Page) => emit('publish', desktop);

    // 呼出导航分组
    const onClickNavGroup = (desktop: Page) => emit('navGroup', desktop);

    // 编辑
    const onClickEdit = (desktop: Page) => emit('edit', desktop);

    // 呼出导航
    const onClickNav = (desktop: Page) => emit('nav', desktop);

    // 删除
    const onClickDelete = (desktop: Page) => emit('delete', desktop);

    // 上线
    const onClickOnline = (desktop: Page) => emit('online', desktop);

    // 下线
    const onClickOffline = (desktop: Page) => emit('offline', desktop);

    // 启用
    const onClickEnable = (desktop: Page) => emit('enable', desktop);

    // 停用
    const onClickDisable = (desktop: Page) => emit('disable', desktop);

    // 新增
    const onClickAdd = () => emit('add');
</script>

<style scoped>


    .card-item {
        height: auto;
        min-height: 280px;
        max-height: 320px;
        transition: all 0.3s ease;
    }

    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 16px;
        height: 100%;
        min-height: 280px;
    }

    :deep(.el-image) {
        width: 100%;
        height: auto;
        aspect-ratio: 16/9;
        min-height: 160px;
    }

    .mt-3 {
        min-height: 40px;
    }

    .scroll-wrapper {
        height: 100%;
        overflow-y: auto;
        padding-right: 4px;
    }
</style>
