<template>
    <el-dialog v-model="props.modelValue" width="40%" @close="onClickCancel" z-index="1986">
        <template #header>
            <span class="text-lg">{{ isEdit ? '编辑导航分组' : '新建导航分组' }}</span>
            {{ siteStore.currentSite?.name }}
        </template>

        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-suffix=":">
            <el-form-item label="导航分组标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入导航分组标题" />
            </el-form-item>
            <el-form-item label="伸缩效果" prop="expandEffect">
                <el-select v-model="form.expandEffect" placeholder="请选择伸缩效果" size="default" clearable>
                    <el-option v-for="item in expandEffectOption"
                               :key="item.code"
                               :label="item.name"
                               :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="导航" prop="navCodes">
                <el-select v-model="form.navCodes" placeholder="请选择导航" size="default" clearable multiple>
                    <el-option v-for="item in navList" :key="item.code" :label="item.title" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector v-model="form.orgId" placeholder="请选择组织" :data="orgTree" label-key="name"
                                    value-key="id" width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="onClickCancel">取消</el-button>
            <el-button type="primary" @click="onClickConfirm">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { FormInstance } from 'element-plus';
    import { Dimension, Nav, NavGroup, Page } from '@/types';
    import { useSiteStore } from '@/stores';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { dimensionApi, navApi } from '@/api';

    // 参数
    const props = defineProps<{
        modelValue: boolean;
        desktop: Page;
        navGroup: Partial<NavGroup>;
    }>();

    // 事件
    const emit = defineEmits(['update:modelValue', 'submit']);

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 伸缩效果枚举
    const expandEffectOption = ref<Enumeration[]>(enumStore.getEnumsByKey('expandEffect') || []);

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 表单引用
    const formRef = ref<FormInstance | null>(null);

    // 表单校验规则
    const rules = {
        title: [{ required: true, message: '请输入导航分组标题', trigger: 'blur' }],
    };

    // 默认导航分组表单
    const defaultNavGroupForm: Partial<NavGroup> = {
        siteCode: siteStore.currentSite?.code,
        desktopCode: props.desktop.code,
        icons: {},
    };

    // 导航分组表单
    const form = ref<Partial<NavGroup>>(defaultNavGroupForm);

    // 是否是编辑
    const isEdit = ref<boolean>(false);

    // 导航列表
    const navList = ref<Nav[]>([]);

    // 重置表单
    const resetForm = () => {
        form.value = defaultNavGroupForm;
        isEdit.value = false;
    };

    // 关闭对话框
    const onClickCancel = () => {
        if (!isEdit.value) {
            resetForm();
        }
        emit('update:modelValue', false);
    };

    // 提交表单
    const onClickConfirm = () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', form.value);
            }
        });
    };

    // 查询导航列表
    const getNavList = async () => {
        const res = await navApi.findNavList({
            siteCode: siteStore.currentSite?.code,
            desktopCode: props.desktop.code,
            delFlag: 0,
            status: 1,
        }, { paged: false });
        if (res.code === 200) {
            navList.value = res.result;
        }
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监控 navGroup
    watch(() => props.navGroup, (newVal) => {
        if (newVal && newVal.id) {
            isEdit.value = true;
            form.value = { ...newVal };
        } else {
            isEdit.value = false;
            resetForm();
        }
    }, { immediate: true });

    onMounted(() => {
        getNavList();
        getOrgTree();
    });
</script>
