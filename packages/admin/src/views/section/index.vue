<template>
    <PageContainer>
        <template #search>
            <section-search @search="handleSearchSection" />
        </template>

        <template #toolbar>
            <section-toolbars
                :sectionSelection="sectionSelection"
                @handleAdd="handleSectionAdd"
                @refreshSectionList="refreshSectionList" />
        </template>

        <template #list>
            <section-list
                ref="sectionListRef"
                :sectionForm="sectionSearchForm"
                @update:selection="handleSelectionSection"
                @edit-section="handleSectionEdit" />
        </template>
  </PageContainer>

    <section-add
            v-model:modelValue="sectionAddDialogVisible"
            :section="section"
            @submit="handleSectionAddSubmit" />
</template>

<script lang="ts" setup>
    import { ref } from 'vue';
    import SectionSearch from '@/views/section/components/section-search.vue';
    import SectionList from '@/views/section/components/section-list.vue';
    import sectionToolbars from '@/views/section/components/section-toolbars.vue';
    import { Section, SectionSearchForm } from '@/types';
    import { sectionApi } from '@/api';
    import SectionAdd from '@/views/section/components/section-add.vue';
    import { useJumpToDesigner } from '@/composables/use-jump-to-designer.ts';
    import { extractDimensions } from '@/utils/extract.ts';
    import { useSiteStore } from '@/stores';
    import { useFeedback } from '@/composables';
    import PageContainer from '../common/page-container.vue';

    defineOptions({
        name: 'SectionIndex',
    });

    // pinia store
    const siteStore = useSiteStore();
    const { jumpToDesigner } = useJumpToDesigner();
    const feedback = useFeedback();

    // 列表组件引用
    const sectionListRef = ref<InstanceType<typeof SectionList> | null>(null);

    // 当前楼层定义
    const section = ref<Section>({} as Section);

    // 楼层查询表单
    const sectionSearchForm = ref<Partial<SectionSearchForm>>({});

    // 已选中的楼层定义列表
    const sectionSelection = ref<Section[]>([]);

    // 新增楼层对话框
    const sectionAddDialogVisible = ref(false);

    // 中转楼层定义查询表单
    const handleSearchSection = (from: Partial<SectionSearchForm>) => {
        sectionSearchForm.value = { ...from };
    };

    // 选择楼层定义
    const handleSelectionSection = (selection: Section[]) => {
        sectionSelection.value = selection;
    };

    // 新增楼层定义
    const handleSectionAdd = () => {
        sectionAddDialogVisible.value = true;
        section.value = {} as Section;
    };

    // 处理编辑楼层定义
    const handleSectionEdit = (data: Section) => {
        section.value = data;
        sectionAddDialogVisible.value = true;
    };

    // 确认新增楼层定义
    const handleSectionAddSubmit = async (data: Section) => {
        sectionAddDialogVisible.value = false;
        if (data.id) {
            const res = await sectionApi.updateSection(data.id, data);
            if (res?.code === 200) {
                refreshSectionList();
                feedback.success('更新楼层定义成功');
            } else {
                feedback.error('更新楼层定义失败：' + res.msg);
            }
        } else {
            const rect = extractDimensions(data.resolution ?? '');
            const section = {
                siteCode: siteStore.currentSiteCode,
                name: data.name,
                resolution: data.resolution,
                type: data.type,
                layout: {
                    layout: {
                        rect: {
                            top: 0,
                            left: 0,
                            width: rect ? rect.width : 0,
                            height: rect ? rect.height : 0,
                        },
                    },
                    props: {},
                },
            };
            Object.assign(data, section);
            const response = await sectionApi.addSection(data);
            if (response && response.code === 200) {
                feedback.success('新增楼层定义成功');
                refreshSectionList();
                jumpToDesigner('section', response?.result?.code);
            } else {
                feedback.error('新增楼层定义失败：' + response.msg);
            }
        }
    };

    // 刷新楼层定义列表
    const refreshSectionList = () => {
        sectionListRef.value?.findSectionList();
    };
</script>
