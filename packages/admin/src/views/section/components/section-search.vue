<template>
    <site-selector />
    <el-form
        inline
        label-width="100px"
        :label-suffix="':'"
        :size="'default'"
        class="mt-2">
        <el-row>
            <el-col :span="5">
                <el-form-item label="类型">
                    <el-select
                        v-model="searchForm.type"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable>
                        <el-option
                            v-for="item in sectionTypeOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="分辨率">
                    <el-select
                        v-model="searchForm.resolution"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable>
                        <el-option
                            v-for="item in resolutionOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item
                    label="组织"
                    prop="orgId">
                    <el-tree-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        :data="orgTree"
                        :props="treeProps"
                        filterable
                        multiple
                        :render-after-expand="false"
                        collapse-tags
                        collapse-tags-tooltip
                        show-checkbox
                        check-strictly
                        check-on-click-node
                        class="tree-select-auto"
                        node-key="id"
                        value-key="id" />
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.name"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="2" :offset="1">
                <el-button
                    text
                    v-if="showAllSearch"
                    @click="showAllSearch = false"
                    style="color: #165dff">
                    <el-icon><ArrowDown /></el-icon>收起
                </el-button>
                <el-button
                    v-else
                    text
                    type="primary"
                    @click="showAllSearch = true"
                    style="color: #165dff">
                    <el-icon><ArrowUp /></el-icon>展开
                </el-button>
            </el-col>
        </el-row>
        <el-row v-if="showAllSearch">
            <el-col :span="5">
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlags"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in delFlagOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.statuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in enableStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Enumeration, useEnumStore, LabelValue } from '@chances/portal_common_core';
    import { SectionSearchForm, Dimension } from '@/types';
    import { useSiteStore } from '@/stores';
    import { dimensionApi } from '@/api/dimension-api.ts';

    const emit = defineEmits(['search']);

    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 删除状态枚举
    const delFlagOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 可用禁用状态枚举
    const enableStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 楼层定义类型枚举
    const sectionTypeOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getEnumsByKey('sectionType') || []);

    // 分辨率枚举
    const resolutionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('resolution') || []);

    const searchForm = ref<Partial<SectionSearchForm>>({
        statuses: [],
        delFlags: [0],
        name: '',
        siteCode: siteStore.currentSiteCode,
    });

    // 搜索条件
    const showAllSearch = ref<boolean>(false);

    const handleSearch = () => {
        emit('search', searchForm.value);
    };

    // tree配置
    const treeProps = {
        children: 'children',
        label: 'name',
        value: 'id',
    }
    
    // 组织树
    const orgTree = ref<Dimension[]>([]);
    
    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监听 siteStore
    watch(() => siteStore.currentSiteCode, (newVal: string) => {
        if (newVal) {
            searchForm.value.siteCode = siteStore.currentSiteCode;
        }
    });

    // 监听查询表单
    watch(() => searchForm.value, () => {
        handleSearch();
    }, { deep: true });

    // 组件挂载时请求数据
    onMounted(() => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
        handleSearch();
        getOrgTree();
    });
</script>
