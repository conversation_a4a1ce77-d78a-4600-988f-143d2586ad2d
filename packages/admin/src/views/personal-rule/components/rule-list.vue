<template>
    <div class="rule-list">
        <div class="main-header">
            <div class="search-area">
                <el-form inline label-width="auto" :label-suffix="':'" :size="'default'" class="mt-2">
                    <el-form-item label="启用状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="请选择"
                            size="default"
                            style="width: 180px"
                            clearable
                            @change="handleSearch"
                        >
                            <el-option
                                v-for="item in enableStatusOption"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="组织">
                        <el-tree-select
                            v-model="searchForm.orgIds"
                            placeholder="请选择"
                            :data="orgTree"
                            :props="treeProps"
                            filterable
                            multiple
                            :render-after-expand="false"
                            collapse-tags
                            collapse-tags-tooltip
                            show-checkbox
                            check-strictly
                            check-on-click-node
                            style="width: 180px"
                            node-key="id"
                            value-key="id"
                        />
                    </el-form-item>
                    <el-form-item label="关键词">
                        <el-input v-model="searchForm.keyword" placeholder="请输入" style="width: 180px" clearable>
                            <template #prefix>
                                <el-icon class="el-input__icon">
                                    <search />
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div class="action-area">
                <el-button
                    :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PERSONAL_RULE.CREATE)"
                    type="primary"
                    @click="handleAdd"
                >
                    新增策略
                </el-button>
            </div>
        </div>
        <div class="body-style">
            <el-table :data="tableData" v-loading="loading" default-expand-all
                    :header-cell-style="{ color: 'black', height: '50px' }"
            >
                <el-table-column prop="name" label="策略名称" show-overflow-tooltip min-width="200px" />
                <el-table-column prop="code" label="策略编码" show-overflow-tooltip  min-width="200px"/>
                <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                            {{ enumStore.getLabelByKeyAndValue('enableStatus', row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="citationCount" label="引用次数" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag :type="row.citationCount > 0 ? 'success' : 'danger'">
                            {{ row.citationCount }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="orgId" label="组织" width="180">
                    <template #default="{ row }">
                        <div>{{ orgOptions.find(org => org.value === row.orgId)?.label }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="createdBy" label="创建人" width="100" />
                <el-table-column prop="createTime" label="创建时间" width="120">
                    <template #default="{ row }">
                        {{ row.createTime ? format(row.createTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="240" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PERSONAL_RULE.EDIT, {type: 'org', value: row.orgId})"
                            type="primary"
                            link
                            @click="handleEdit(row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            :disabled="row.citationCount > 0 || !canDeleteByAdmin(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PERSONAL_RULE.DELETE, {type: 'org', value: row.orgId})"
                            type="danger"
                            link
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>
                        <el-button
                            :disabled="!canEnable(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PERSONAL_RULE.ENABLE, {type: 'org', value: row.orgId})"
                            v-if="canEnable(row)"
                            type="success"
                            link
                            @click="handleEnable(row)"
                        >
                            启用
                        </el-button>
                        <el-button
                            :disabled="!canDisable(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PERSONAL_RULE.DISABLE, {type: 'org', value: row.orgId})"
                            v-if="canDisable(row)"
                            type="danger"
                            link
                            @click="handleDisable(row)"
                        >
                            禁用
                        </el-button>
                        <el-button
                            type="primary"
                            link
                            @click="handleReferenceManage(row)"
                        >
                            引用管理
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                :total="totalElements"
                :current-page-value="currentPage"
                @update:currentPageValue="handleCurrentChange"
                @update:pageSizeValue="handleSizeChange"
            />
        </div>
        <rule-sync-dialog
            v-model:visible="syncDialogVisible"
            v-model:mode="dialogMode"
            :rule-code="currentRule?.code"
            :sync-data="syncData"
            :rule-options="ruleOptions"
            @replace="handleReplaceConfirm"
        />
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Search } from '@element-plus/icons-vue';
    import { cmsApi, personalRuleApi } from '@/api';
    import { PersonalRule, PersonalRuleSearchForm, Dimension } from '@/types';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import RuleSyncDialog from './rule-sync-dialog.vue';
    import { DEFAULT_PAGE_SIZE } from '@/utils/constants';
    import { format } from 'date-fns';
    import { ADMIN_BIZ_PERMISSION, canDeleteByAdmin, canDisable, canEnable } from '@/utils';
    import { dimensionApi } from '@/api/dimension-api.ts';
    import { useFeedback } from '@/composables';

    // 参数
    const props = defineProps<{
        currentNode: any;
    }>();

    // 事件
    const emit = defineEmits(['add', 'edit', 'delete', 'enable', 'disable']);

    // pinia store
    const feedback = useFeedback();
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);
    const loading = ref(false);

    // 状态
    const enableStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 区域
    const areaCodesOption = ref<any[]>([]);

    // 搜索表单
    const searchForm = ref<PersonalRuleSearchForm>({} as PersonalRuleSearchForm);

    // 表格数据
    const tableData = ref<PersonalRule[]>([] as PersonalRule[]);

    // 本地响应式变量存储当前节点
    const localCurrentNode = ref(props.currentNode);

    // 同步对话框数据
    const syncDialogVisible = ref(false);
    const syncData = ref<Record<string, any[]>>({});
    const currentRule = ref<any>(null);

    // 对话框模式
    const dialogMode = ref<'view' | 'sync' | 'replace'>('view');

    // 可选策略列表
    const ruleOptions = ref<PersonalRule[]>([] as PersonalRule[]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1; // 切换 pageSize 需要回到第一页
        getPersonalRuleList();
    };

    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        getPersonalRuleList();
    };

    // 搜索事件
    const handleSearch = () => {
        getPersonalRuleList();
    };

    // 新增策略
    const handleAdd = () => {
        if (!props.currentNode) {
            feedback.error('请先选择目录节点');
            return;
        }
        emit('add');
    };

    // 编辑策略
    const handleEdit = (row: PersonalRule) => {
        emit('edit', row);
    };

    // 启用
    const handleEnable = async (row: PersonalRule) => {
        if (await feedback.confirm(`确定要启用该策略吗？`, '确认操作', 'warning')) {
            try {
                await personalRuleApi.enablePersonalRule(row.code);
                feedback.success('启用成功');
                getPersonalRuleList();
            } catch (error) {
                feedback.error('启用失败');
            }
        }
    };

    // 禁用
    const handleDisable = async (row: PersonalRule) => {
        if (await feedback.confirm(`确定要禁用该策略吗？`, '确认操作', 'warning')) {
            try {
                await personalRuleApi.disablePersonalRule(row.code);
                feedback.success('禁用成功');
                getPersonalRuleList();
            } catch (error) {
                console.error('禁用失败:', error);
                feedback.error('禁用失败');
            }
        }
    };

    // 删除策略
    const handleDelete = async (row: any) => {
        if (await feedback.confirm(`确定要删除该策略吗？`, '确认操作', 'warning')) {
            try {
                await personalRuleApi.deletePersonalRule(row.code);
                feedback.success('删除成功');
                getPersonalRuleList();
            } catch (error) {
                console.error('删除失败:', error);
                feedback.error('删除失败');
            }
        }
    };

    // 获取可选策略列表
    const getRuleOptions = async () => {
        try {
            const res = await personalRuleApi.getAllPersonalRules();
            if (res.code === 200) {
                ruleOptions.value = res.result;
            }
        } catch (error) {
            console.error('获取策略列表失败:', error);
            feedback.error('获取策略列表失败');
        }
    };

    // 引用管理
    const handleReferenceManage = async (row: any) => {
        try {
            currentRule.value = row;
            dialogMode.value = 'view';
            // 获取同步数据
            const res = await personalRuleApi.getSyncData(row.code);
            if (res.code === 200) {
                syncData.value = res.result;
                syncDialogVisible.value = true;
            }
        } catch (error) {
            console.error('获取引用数据失败:', error);
            feedback.error('获取引用数据失败');
        }
    };

    // 处理替换确认
    const handleReplaceConfirm = async (params: { data: Record<string, any[]>; targetCode: string }) => {
        try {
            if (!currentRule.value?.code) return;

            await personalRuleApi.replaceReference(params.targetCode, params.data);
            feedback.success('替换成功');
            getPersonalRuleList();
            syncDialogVisible.value = false;
        } catch (error) {
            console.error('替换失败:', error);
            feedback.error('替换失败');
        }
    };

    // 获取策略列表
    const getPersonalRuleList = async () => {
        if (!localCurrentNode.value?.code) return;

        searchForm.value.folderCode = localCurrentNode.value.code;
        const response = await personalRuleApi.getPersonalRuleList(searchForm.value, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'id,desc',
        });
        if (response.code === 200) {
            tableData.value = response.result;
            totalElements.value = Number(response.page.totalElements);
        }
        loading.value = false;
    };

    const getAreaCodesOption = async () => {
        const enumParam = {
            'codes': ['regionEnum'],
        };
        const res = await cmsApi.searchEnum(enumParam);
        if (res.code === 200) {
            res.result[0].children.forEach((item: any) => {
                item.children.forEach((item: any) => {
                    areaCodesOption.value.push({
                        label: item.name,
                        value: item.code,
                    });
                });
            });
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };
        // tree配置
        const treeProps = {
        children: 'children',
        label: 'name',
        value: 'id',
    }
    
    // 组织树
    const orgTree = ref<Dimension[]>([]);
    
    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监听查询表单
    watch(() => searchForm.value, () => {
        handleSearch();
    }, { immediate: true, deep: true });

    // 监听props变化
    watch(() => props.currentNode, (newNode) => {
        localCurrentNode.value = newNode;
        if (newNode) {
            getPersonalRuleList();
        }
    }, { immediate: true, deep: true });

    // 监听对话框模式变化
    watch(dialogMode, async (newMode) => {
        if (newMode === 'replace') {
            await getRuleOptions();
        }
    });

    onMounted(() => {
        getAreaCodesOption();
        getOrgOptions();
        getOrgTree();
    });

    defineExpose({
        getPersonalRuleList,
    });
</script>

<style scoped>
.rule-list {
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
}

.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    background: white;
    border-radius: 10px;
    padding: 10px;
}

.search-area {
    display: flex;
    align-items: center;
    gap: 16px;
}

.action-area {
    display: flex;
    gap: 16px;
}
.body-style {
    height: 100%;
    background: white;
    border-radius: 10px;
    padding: 10px
}
.tree-select-auto {
    min-width: 180px;
    max-width: 600px;
    width: auto;
}
</style>

