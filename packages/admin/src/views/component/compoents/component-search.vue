<template>
    <site-selector />
    <el-form
        inline
        label-width="100"
        :label-suffix="':'"
        :size="'default'"
        class="mt-2">
        <el-row>
            <el-col :span="5">
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.delFlag"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple
                        @change="handleSearch">
                        <el-option
                            v-for="item in delFlagOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="组织">
                    <el-tree-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        :data="orgTree"
                        :props="treeProps"
                        filterable
                        multiple
                        :render-after-expand="false"
                        collapse-tags
                        collapse-tags-tooltip
                        show-checkbox
                        check-strictly
                        check-on-click-node
                        style="width: 180px"
                        node-key="id"
                        value-key="id"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.name"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { useSiteStore } from '@/stores';
    import { ComponentSearchForm, Dimension } from '@/types';
    import { dimensionApi } from '@/api/dimension-api.ts';

    // 事件
    const emit = defineEmits(['search']);

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // tree配置
    const treeProps = {
        children: 'children',
        label: 'name',
        value: 'id',
    }
    
    // 组织树
    const orgTree = ref<Dimension[]>([]);
    
    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 删除状态枚举
    const delFlagOption = ref<{ name: any; code: number; }[]>(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 查询表单
    const searchForm = ref<Partial<ComponentSearchForm>>({
        siteCode: '',
        delFlags: [0],
        name: '',
        orgIds: []
    });

    // 查询
    const handleSearch = () => {
        emit('search', searchForm.value);
    };

    // 监听 siteStore
    watch(() => siteStore.currentSiteCode, () => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
    });

    // 监听查询表单
    watch(() => searchForm.value, () => {
        console.log("aaaa:"+ searchForm.value.orgIds)
        handleSearch();
    }, { deep: true });

    onMounted(() => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
        handleSearch();
        getOrgTree();
    });
</script>
<style scoped>
.tree-select-auto {
    min-width: 180px;
    max-width: 600px;
    width: auto;
}
</style>

