<template>
    <PageContainer>
        <template #search>
            <component-search @search="handleSearchComponent" />
        </template>

        <template #toolbar>
            <component-toolbars
                :comPonentSelection="componentSelection"
                @handleUploadPackage="handleUploadPackage"
                @refreshComponentList="refreshComponentList" />
        </template>

        <template #list>
            <component-list
                ref="componentListRef"
                :component-search-form="componentSearchForm"
                @update:selection="handleSelection" />
        </template>
    </PageContainer>
    <upload-component-package
            :uploadDialogVisible="uploadDialogVisible"
            @update:uploadDialogVisible="uploadDialogVisible = $event" />
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import ComponentSearch from '@/views/component/compoents/component-search.vue';
    import ComponentList from '@/views/component/compoents/component-list.vue';
    import ComponentToolbars from '@/views/component/compoents/compoent-toolbars.vue';
    import { Component, ComponentSearchForm } from '@/types';
    import PageContainer from '../common/page-container.vue';

    defineOptions({
        name: 'Component',
    });

    const componentSearchForm = ref<Partial<ComponentSearchForm>>({
        // 网站编码
        siteCode: '',
        delFlags: [],
        name: '',
        orgIds: [] as number[]
    });

    // 绑定子组件
    const componentListRef = ref<InstanceType<typeof ComponentList> | null>(null);

    const componentSelection = ref<Component[]>([]);

    const uploadDialogVisible = ref(false);

    // 调用子组件的方法
    const refreshComponentList = () => {
        componentListRef.value?.findComponentList();
    };

    // 中转组件查询表单
    const handleSearchComponent = (data: Partial<ComponentSearchForm>) => {
        componentSearchForm.value = data;
    };

    // 选中的组件
    const handleSelection = (val: Component[]) => {
        componentSelection.value = val;
    };

    const handleUploadPackage = () => {
        uploadDialogVisible.value = true;
    };
</script>
