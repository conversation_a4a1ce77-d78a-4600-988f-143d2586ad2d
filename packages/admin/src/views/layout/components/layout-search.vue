<template>
    <site-selector />
    <el-form inline label-width="100" :label-suffix="':'" :size="'default'" class="mt-2">
        <el-row>
            <el-col :span="5">
                <el-form-item label="布局类型">
                    <el-select
                        v-model="searchForm.types"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple
                    >
                        <el-option
                            v-for="item in layoutTypeOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.statuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple
                    >
                        <el-option
                            v-for="item in enableStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>

            </el-col>
            <el-col :span="5">
                <el-form-item label="组织">
                    <el-tree-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        :data="orgTree"
                        :props="treeProps"
                        filterable
                        multiple
                        :render-after-expand="false"
                        collapse-tags
                        collapse-tags-tooltip
                        show-checkbox
                        check-strictly
                        check-on-click-node
                        style="width: 180px"
                        node-key="id"
                        value-key="id"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.name" placeholder="请输入" style="width: 180px" clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="2" :offset="1">
                <el-button
                    text
                    v-if="showAllSearch"
                    @click="showAllSearch = false"
                    style="color: #165DFF;">
                    <el-icon><ArrowDown /></el-icon>收起
                </el-button>
                <el-button
                    v-else
                    text
                    type="primary"
                    @click="showAllSearch = true"
                    style="color: #165DFF;">
                    <el-icon><ArrowUp /></el-icon>展开
                </el-button>
            </el-col>
            <el-row v-if="showAllSearch">
                <el-col :span="5">
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlags"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple
                    >
                        <el-option
                            v-for="item in delFlagOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            </el-row>
        </el-row>
    </el-form>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Enumeration, useEnumStore, LabelValue } from '@chances/portal_common_core';
    import { LayoutParam , Dimension} from '@/types';
    import { useSiteStore } from '@/stores';
    import { dimensionApi } from '@/api/dimension-api.ts';

    const emit = defineEmits(['search']);

    // 枚举
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 搜索条件
    const showAllSearch = ref<boolean>(false);

    // 可用禁用状态枚举
    const enableStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 删除状态枚举
    const delFlagOptions = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 布局类型枚举
    const layoutTypeOption = ref<Enumeration[]>(enumStore.getEnumsByKey('layoutType') || []);

    // 查询表单
    const searchForm = ref<Partial<LayoutParam>>({
        name: '',
        statuses: [] as number[],
        delFlags: [0] as number[],
        types: [] as string[],
        siteCode: '',
        orgIds: [] as number[],
    });

    // 查询
    const handleSearch = () => {
        emit('search', searchForm.value);
    };

    // tree配置
    const treeProps = {
        children: 'children',
        label: 'name',
        value: 'id',
    }
    
    // 组织树
    const orgTree = ref<Dimension[]>([]);
    
    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监听 siteStore
    watch(() => siteStore.currentSiteCode, () => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
    });

    // 监听查询表单
    watch(() => searchForm.value, () => {
        handleSearch();
    }, { deep: true });

    // 组件挂载时请求数据
    onMounted(() => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
        handleSearch();
        getOrgTree();
    });
</script>

<style scoped>
    .component-search-input ::v-deep(.el-input__wrapper) {
        background-color: #f2f2f2 !important;
    }
</style>
<style scoped>
.tree-select-auto {
    min-width: 180px;
    max-width: 600px;
    width: auto;
}
</style>

