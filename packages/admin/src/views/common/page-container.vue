<template>
  <div class="page-container">
    <div class="search-style" v-if="showSearch">
      <slot name="search" />
    </div>
    <div class="toolbars-style" v-if="showToolbars">
      <slot name="toolbar" />
    </div>
    <div class="body-style" v-if="showList">
      <slot name="list" />
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  showToolbars: {
    type: Boolean,
    default: true,
  },
  showSearch: {
    type: Boolean,
    default: true,
  },
  showList: {
    type: Boolean,
    default: true,
  }
});
</script>
<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height:  calc(100vh - 135px);
  box-sizing: border-box;
  background: #f5f5f5;
  overflow: hidden; /* 避免外层滚动 */
}
.search-style,
.toolbars-style {
  background: white;
  border-radius: 0 0 10px 10px;
  padding: 10px;
  margin-bottom: 10px;
  flex-shrink: 0;
}
.body-style {
  background: white;
  border-radius: 10px;
  padding: 10px 10px 0 10px;
  flex: 1;
  overflow: hidden; /* 避免外层滚动 */
  display: flex;
  flex-direction: column;
  min-height: 0;
}
</style>
