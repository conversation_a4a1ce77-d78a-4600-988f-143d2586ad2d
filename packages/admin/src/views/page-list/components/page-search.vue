<template>
    <site-selector />
    <el-form
        inline
        label-width="100"
        :label-suffix="':'"
        :size="'default'"
        class="mt-2">
        <el-row>
            <el-col :span="5">
                <el-form-item label="标签">
                    <el-input
                        v-model="searchForm.tags"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable />
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="业务分组">
                    <el-select
                        v-model="searchForm.bizGroups"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in bizGroupOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item
                    label="组织"
                    prop="orgId">
                    <el-tree-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        :data="orgTree"
                        :props="treeProps"
                        filterable
                        multiple
                        :render-after-expand="false"
                        collapse-tags
                        collapse-tags-tooltip
                        show-checkbox
                        check-strictly
                        check-on-click-node
                        class="tree-select-auto"
                        node-key="id"
                        value-key="id" />
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.name"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="2" :offset="1">
                <el-button
                    text
                    v-if="showAllSearch"
                    @click="showAllSearch = false"
                    style="color: #165DFF;">
                    <el-icon><ArrowDown /></el-icon>收起
                </el-button>
                <el-button
                    v-else
                    text
                    type="primary"
                    @click="showAllSearch = true"
                    style="color: #165DFF;">
                    <el-icon><ArrowUp /></el-icon>展开
                </el-button>
            </el-col>
        </el-row>
        <el-row v-if="showAllSearch">
            <el-col :span="5">
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.statuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in enableStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="上下线状态">
                    <el-select
                        v-model="searchForm.onlineStatuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in onlineStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="审核状态">
                    <el-select
                        v-model="searchForm.auditStatuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in auditStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="5">
                <el-form-item label="可见状态">
                    <el-select
                        v-model="searchForm.visibleStatuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in visibleStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="2" :offset="1"></el-col>
            <el-col :span="5">
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlags"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        multiple>
                        <el-option
                            v-for="item in delFlagOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup lang="ts">
    import { defineEmits, onMounted, ref, watch } from 'vue';
    import { LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { PageSearchForm , Dimension} from '@/types';
    import { useSiteStore } from '@/stores';
    import { dimensionApi } from '@/api/dimension-api.ts';
    import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

    // 事件
    const emit = defineEmits(['search-page']);

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 删除状态枚举
    const delFlagOptions = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    // 审核状态枚举
    const auditStatusOptions = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('auditStatus') || []);

    // 可用禁用状态枚举
    const enableStatusOption = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 可见枚举
    const visibleStatusOptions = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('visibleStatus') || []);

    // 可见枚举
    const bizGroupOptions = ref<{
        label: string;
        value: string;
    }[]>(enumStore.getOptionsByKey('bizGroup') || []);

    // tree配置
    const treeProps = {
        children: 'children',
        label: 'name',
        value: 'id',
    }
    
    // 组织树
    const orgTree = ref<Dimension[]>([]);
    
    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };
    
    // 上线枚举
    const onlineStatusOptions = ref<{
        name: any;
        code: number;
    }[]>(enumStore.getNameCodeNumberOptionsByKey('onlineStatus') || []);

    // 搜索条件
    const showAllSearch = ref<boolean>(false);

    // 搜索表单
    const searchForm = ref<Partial<PageSearchForm>>({
        delFlags: [0] as number[],
    });

    // 条件查询页面列表
    const handleSearch = () => {
        emit('search-page', searchForm.value);
    };

    // 监听 siteStore
    watch(() => siteStore.currentSiteCode, () => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
    });

    // 监听查询表单
    watch(() => searchForm.value, () => {
        handleSearch();
    }, { deep: true });

    // 组件挂载时请求数据
    onMounted(() => {
        searchForm.value.siteCode = siteStore.currentSiteCode;
        getOrgTree();
    });
</script>
<style scoped>
.tree-select-auto {
    min-width: 180px;
    max-width: 600px;
    width: auto;
}
</style>
