import { Nav, Page } from '@/types/entity.ts';

// 管理状态查询表单
export interface AdminStatusSearchForm {
    // 删除状态
    delFlag: number;

    // 可用状态
    status: number;

    // 删除状态列表
    delFlags: number[];

    // 启用状态列表
    statuses: number[];
}

// 发布状态查询表单
export interface PublishStatusSearchForm extends AdminStatusSearchForm {
    // 审核状态
    auditStatus: number;

    // 上线状态
    onlineStatus: number;

    // 可见状态
    visibleStatus: number;

    // 审核状态列表
    auditStatuses: number[];

    // 上线状态列表
    onlineStatuses: number[];

    // 可见状态列表
    visibleStatuses: number[];
}

// 导航查询表单
export interface NavSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 桌面编码
    desktopCode: string;

    // 页面编码
    pageCode: string;

    // 类型
    type: number;

    // 名称
    title: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];
}

// 导航新增表单
export interface NavForm extends Nav {
    // 关联页面
    page: Partial<Page>;
}

// 导航分组查询表单
export interface NavGroupSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 桌面编码
    desktopCode: string;

    // 名称
    title: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];
}

// 页面查询表单
export interface PageSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 业务分组
    bizGroups: string[];

    // 标签
    tags: string;

    // 组织id
    orgIds: number[];
}

// 桌面查询表单
export interface DesktopSearchForm extends PublishStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 标签
    tags: string;

    // 标签列表
    tagsList: string[];
}

// 推荐策略查询表单
export interface PersonalRuleSearchForm extends AdminStatusSearchForm {
    // 名称
    name: string;

    // 布局编码
    code: string;

    // 推荐策略目录编码
    folderCode: string;

    // 布局编码列表
    codes: string[];

    // 关键字
    keyword: string;

    // 组织id
    orgIds: number[];
}

// 网站查询表单
export interface SiteSearchForm extends AdminStatusSearchForm {
    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 数据域
    domain: string;

    // 组织id
    orgIds: number[];
}

// 跳转链接查询表单
export interface LinkTypeSearchForm extends AdminStatusSearchForm {
    // 网站编码
    siteCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string;

    // 类型
    dataType: string;

    // 组织id
    orgIds: number[];
}

export interface EnumParam {
    // codes
    codes: string[];
}

// 楼层定义查询表单
export interface SectionSearchForm extends AdminStatusSearchForm {

    // 网站编码
    siteCode: string;

    // 布局编码
    layoutCode: string;

    // 名称
    name: string;

    // 编码
    code: string;

    // 编码列表
    codes: string[];

    // 类型
    type: string;

    // 标签
    tags: string;

    // 标签列表
    tagsList: string[];

    // 分辨率
    resolution: string;

    // 作用域
    scope: string;

    // 创建者
    createdBy: string;

    // 组织id
    orgIds: number[];
}

// 布局查询表单
export interface LayoutParam extends AdminStatusSearchForm {
    // 名称
    name: string;

    // 类型
    types: string[];

    // 网站编码
    siteCode: string;

    // 组织id
    orgIds: number[];
}

// 组件查询表单
export interface ComponentSearchForm extends AdminStatusSearchForm {
    // 名称
    name: string;

    // 网站编码
    siteCode: string;

    // 组织id
    orgIds: number[];
}
