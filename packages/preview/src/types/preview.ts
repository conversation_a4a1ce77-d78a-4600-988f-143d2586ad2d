export interface JsvDate {
  aidlData: AidlData,
  loginData: LoginData,
}
export interface AidlData {
  // 机顶盒ID(32位)
  STBID: string,
  // 机顶盒型号
  STBType: string,
  // 固件版本号
  SoftwareVersion: string,
  // 机顶盒IP地址
  IP: string,
  // 机顶盒有线MAC地址
  MAC: string,
  // 中移动设备序号(15位)
  DeviceID: string,
  // 设备播放编码(15位)
  DeviceCode: string,
  // 业务账号
  UserID: string,
  // 中兴sessionid(获取播放地址用)
  jsessionid: string,
  UserToken: string,
  EPGServerURL: string,
  LastchannelNum: string,
  Reserved: string,
  Vendor: string,
  SupportMultiPlayer: string,
  platform: string,
  epgUrl: string,
  previewTime: string
}

export interface LoginData {
  // 业务账号
  userId: string,
  // 用户分组
  userGroup: string,
  // 用户区域
  areaCode: string,
  // 子区域
  subAreaCode: string,
  // 服务器地址 json
  servers: Servers
}

export interface Servers {
  biServer2: string,
  epgServerBackup: string,
  aaaServer: string,
  appStoreIndex: string,
  youkuProxyServer: string,
  updateServer: string,
  upgradeServer: string,
  aiqiyiProxyServer: string,
  orderServerBackup: string,
  searchServer: string,
  appStoreLoginServer: string,
  tencentProxyServe: string,
  epgIndex: string,
  aaaServerBackup: string,
  biServer: string,
  imageServer: string,
  epgIndex2: string,
  epgIndex3: string,
  webIndex: string,
  epgIndex4: string,
  orderServer: string,
  logServer: string,
  updateServerBackup: string,
  epgServer: string,
  userServerBackup: string,
  userServer: string,
  webServer: string
}