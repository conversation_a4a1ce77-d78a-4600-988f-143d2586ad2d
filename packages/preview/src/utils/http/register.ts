import { CmsApi, RealCmsApi,PreviewApi, RealPreviewApi } from '@/api';
import { MockCmsApi, MockPreviewApi } from '@/mock';
import { http } from '@/utils/http/index.ts';

const apiFactory = http.getApiFactory();

// 注册 CMS API
apiFactory.register('CmsApi', RealCmsApi, MockCmsApi);
const cmsApi: CmsApi = http.getApi('CmsApi');

// 注册 CMS API
apiFactory.register('PreviewApi', RealPreviewApi, MockPreviewApi);
const previewApi: PreviewApi = http.getApi('PreviewApi');

export {
    cmsApi,
    previewApi,
};
