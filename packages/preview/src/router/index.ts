import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
import { scopedStyleWrapper } from '@/utils/scoped-style-wrapper';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/preview',
        name: 'PREVIEW',
        meta: {
            componentName: 'PREVIEW',
            layout: 'designer',
        },
        component: scopedStyleWrapper(() => import('@/views/preview.vue'), 'Preview'),
    },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export { routes };
export default router;
